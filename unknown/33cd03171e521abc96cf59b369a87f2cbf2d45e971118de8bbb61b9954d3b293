{"version": "0.2", "language": "en", "words": ["alphanum", "DIQU", "Kvnwr", "Zgoh", "QUDRX", "MILIS", "ength", "strongpassword", "weakpassword", "apikey", "requestid", "dtos", "blabla", "randexp", "mediumpassword", "ntegral", "headerapikey", "apikeys", "maxlength", "openxmlformats", "officedocument", "spreadsheetml", "DDTHH", "casl", "Streamable", "superadmin", "userhistories", "userpasswords", "VJMV", "golevelup", "abcde<PERSON><PERSON><PERSON><PERSON><PERSON>", "ijkl", "ssword", "safestring", "testuser", "emiting", "presignUrl", "presign", "presigner", "presigned", "bullmq", "uuidv", "cgst", "sgst", "igst", "nodemon", "checkin", "CHECKEDIN", "Payrate", "hopwellness", "luxon", "promotionitems", "lastcounter", "ONCEADAY", "appt", "pricings", "hsnor", "schedulings", "subsettings"], "ignorePaths": ["node_modules/**", "endpoints/**", "*coverage/**", ".husky/**", ".github/**", "dist/**", "logs/**", "**/**/*.json"]}