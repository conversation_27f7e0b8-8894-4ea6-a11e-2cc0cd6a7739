import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { DurationUnit } from 'src/utils/enums/duration-unit.enum';
import { InvoiceStatus } from 'src/utils/enums/invoice-status.enum';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { PaymentMethod } from 'src/utils/enums/paymentMethod.enum';

export type InvoiceDocument = Invoice & Document;

@Schema({ timestamps: true })
export class Invoice {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    createdBy: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: 'User' })
    cancelledBy: string;

    @Prop({ type: Date, required: true, default: Date.now() })
    invoiceDate: Date;

    @Prop({ type: Number, required: true })
    invoiceNumber: number;

    @Prop({ type: Number, required: true })
    orderId: number;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;

    @Prop({
        type: [
            {
                packageId: { type: SchemaTypes.ObjectId, required: true, ref: "Pricing" },
                purchaseIds: { type: [SchemaTypes.ObjectId], required: true, ref: "PurchasedPackages" },
                packageName: { type: String, required: true },
                quantity: { type: Number, required: true, default: 1 },
                isBundledPricing: { type: Boolean, required: false },
                expireIn: { type: Number, required: true },
                durationUnit: { type: String, required: true, enum: DurationUnit },
                startDate: { type: Date, required: true },
                endDate: { type: Date, required: true },
                unitPrice: { type: Number, required: true },
                discountType: { type: String, required: false, enum: DiscountType },
                discountedBy: { type: SchemaTypes.ObjectId, required: false, default: null, ref: "User" },
                discountValue: { type: Number, required: false },
                discountExcludeCart: { type: Number, required: true },
                discountIncludeCart: { type: Number, required: true },
                hsnOrSacCode: { type: String, required: false },
                tax: { type: Number, required: true, default: 0 },
                gstAmount: { type: Number, required: true, default: 0 },
                promotionLabel: { type: String, required: false },
                promotionLabelKey: { type: String, required: false },


            },
        ],
        required: true,
    })
    purchaseItems: {
        packageId: string;
        purchaseIds: string[];
        packageName: string;
        quantity: number;
        isBundledPricing?: boolean;
        expireIn: number;
        durationUnit: DurationUnit;
        startDate: Date;
        endDate: Date;
        unitPrice: number;
        discountType?: DiscountType;
        discountValue?: number;
        discountedBy?: string;
        discountExcludeCart: number;
        discountIncludeCart: number;
        hsnOrSacCode?: string;
        tax: number;
        gstAmount: number;
        promotionLabel?: string;
        promotionLabelKey?: string

    }[];

    @Prop({
        type: [
            {
                productId: { type: SchemaTypes.ObjectId, required: true, ref: 'Product' },
                productVariantId: { type: SchemaTypes.ObjectId, required: false, ref: 'ProductVariant' },
                productName: { type: String, required: true },
                quantity: { type: Number, required: true, default: 1 },
                salePrice: { type: Number, required: true },
                mrp: { type: Number, required: true },
                finalPrice: { type: Number, required: true },
                discountType: { type: String, required: false, default: "percentage" },
                discountValue: { type: Number, required: false },
                discountedBy: { type: SchemaTypes.ObjectId, required: false, default: null, ref: "User" },
                discountExcludeCart: { type: Number, required: true },
                discountIncludeCart: { type: Number, required: true },
                hsnOrSacCode: { type: String, required: false },
                tax: { type: Number, required: true, default: 0 },
                gstAmount: { type: Number, required: true, default: 0 },
            },
        ],
        required: true,
    })
    productItem: {
        productId: string;
        productVariantId: string;
        productName: string;
        quantity: number;
        salePrice: number;
        mrp: number;
        finalPrice: number;
        discountType?: string;
        discountValue?: number;
        discountedBy?: string;
        discountExcludeCart: number;
        discountIncludeCart: number;
        hsnOrSacCode?: string;
        tax: number;
        gstAmount: number;
    }[];

    @Prop({
        type: [
            {
                customPackageId: { type: SchemaTypes.ObjectId, required: true, ref: "CustomPackage" },
                packageName: { type: String, required: true },
                quantity: { type: Number, required: true, default: 1 },
                unitPrice: { type: Number, required: true },
                discountType: { type: String, required: false, enum: DiscountType },
                discountValue: { type: Number, required: false },
                discountExcludeCart: { type: Number, required: true },
                discountIncludeCart: { type: Number, required: true },
                hsnOrSacCode: { type: String, required: false },
                tax: { type: Number, required: true, default: 0 },
                gstAmount: { type: Number, required: true, default: 0 },
                isReturned: {
                    type: Boolean, require: false, default: false
                },
                returnDate: {
                    type: Date, require: false, default: null
                }
            },
        ],
        required: true,
    })
    customPackageItems: {
        customPackageId: string;
        packageName: string;
        quantity: number;
        unitPrice: number;
        discountType?: DiscountType;
        discountValue?: number;
        discountExcludeCart: number;
        discountIncludeCart: number;
        hsnOrSacCode?: string;
        tax: number;
        gstAmount: number;
        isReturned?: boolean;
        returnDate?: Date;

    }[];

    @Prop({ type: Number, required: true })
    subTotal: number;

    @Prop({ type: Number, required: false, default: 0 })
    discount?: number;

    @Prop({ type: Number, required: false, default: 0 })
    itemDiscount?: number;

    @Prop({ type: Number, required: false, default: 0 })
    returnDiscount?: number;

    @Prop({ type: SchemaTypes.ObjectId, required: false, default: null, ref: "User" })
    discountedBy?: string;

    @Prop({ type: Number, required: false, default: 0 })
    cartDiscount?: number;

    @Prop({ type: Number, required: false, default: 0 })
    cartDiscountAmount?: number;
    @Prop({ type: String, required: false, })
    cartDiscountType?: string;

    @Prop({ type: Number, required: false, default: 0 })
    totalGstValue: number;

    @Prop({ type: Number, required: false, default: 0 })
    totalAmountAfterGst: number;

    @Prop({ type: Number, required: false, default: 0 })
    roundOff: string;

    @Prop({ type: Number, required: true })
    grandTotal: number;

    @Prop({ type: Number, required: false })
    amountPaid: number;

    @Prop({ type: String, required: true })
    amountInWords: string;

    @Prop({ type: String, required: true, enum: PaymentStatus, default: PaymentStatus.COMPLETED })
    paymentStatus: PaymentStatus;

    @Prop({ type: String, required: false })
    paymentReason: string;

    @Prop({
        type: [
            {
                paymentMethod: { type: String, required: true },
                paymentMethodId: { type: SchemaTypes.ObjectId, required: true, ref: "Facility.paymentMethodId" },
                paymentGateway: { type: String, required: false },
                transactionId: { type: String, required: false },
                amount: { type: Number, required: true },
                paymentDate: { type: Date, required: true },
                paymentStatus: { type: String, required: false, enum: PaymentStatus },
                description: { type: String, required: false },
                denominations: {
                    type: Map,
                    of: Number,
                    required: function () { return this.paymentMethod === PaymentMethod.CASH; },
                },
            }
        ],
        required: false,
    })
    paymentDetails: {
        paymentMethod: string;
        paymentMethodId: string;
        paymentGateway?: string;
        transactionId?: string;
        amount: number;
        paymentDate: Date;
        paymentStatus?: string;
        description?: string;
        denominations?: Record<number, number>;
    }[];

    @Prop({ required: false, default: false })
    isSplittedPayment: boolean;

    @Prop({ type: String, required: false })
    platform?: string;

    @Prop({ type: String, required: false, default: InvoiceStatus.PENDING })
    invoiceStatus?: string;

    @Prop({ type: String, required: false })
    refundStatus?: string;

    @Prop({ type: Number, required: false })
    refundAmount?: number;

    @Prop({ type: String, required: false })
    invoiceFilePath?: string;

    @Prop({
        type: {
            customerId: { type: String, required: true },
            name: { type: String, required: true },
            email: { type: String, required: false },
            phone: { type: String, required: false },
        },
        required: true,
    })
    clientDetails: {
        customerId: string;
        name: string;
        email?: string;
        phone: string;
    };

    @Prop({
        type: {
            customerId: { type: String, required: true },
            name: { type: String, required: true },
            email: { type: String, required: false },
            phone: { type: String, required: false },
            addressLine1: { type: String, required: false },
            addressLine2: { type: String, required: false },
            postalCode: { type: Number, required: false },
            cityId: { type: SchemaTypes.ObjectId, ref: "Cities", required: false },
            cityName: { type: String, required: false },
            stateId: { type: SchemaTypes.ObjectId, ref: "State", required: true },
            stateName: { type: String, required: true },
            gstNumber: { type: String, required: function () { return this.isForBusiness === true; }, default: "" },
            utCode: { type: String, required: true },
        },
        required: true,
    })
    clientBillingDetails: {
        customerId: string;
        name: string;
        addressLine1?: string;
        addressLine2?: string;
        postalCode?: number;
        cityId?: string;
        cityName?: string;
        stateId: string;
        stateName: string;
        gstNumber?: string;
        email?: string;
        phone?: string;
        utCode: string;
    };

    @Prop({
        type: {
            facilityName: { type: String, required: true },
            billingName: { type: String, required: true },
            gstNumber: { type: String, required: false },
            email: { type: String, required: true },
            phone: { type: String, required: true },
            addressLine1: { type: String, required: false },
            addressLine2: { type: String, required: false },
            postalCode: { type: Number, required: false },
            cityId: { type: SchemaTypes.ObjectId, ref: "Cities", required: true },
            cityName: { type: String, required: true },
            stateId: { type: SchemaTypes.ObjectId, ref: "State", required: true },
            stateName: { type: String, required: true },
            utCode: { type: String, required: true },
        },
        required: true,
    })
    billingDetails: {
        facilityName: string;
        billingName: string;
        gstNumber?: string;
        email: string;
        phone: string;
        addressLine1: string;
        addressLine2?: string;
        postalCode?: number;
        cityId: string;
        cityName: string;
        stateId: string;
        stateName: string;
        utCode: string;
    };

    @Prop({
        type: {
            returnPurchaseIds: [{ type: SchemaTypes.ObjectId, ref: 'invoices', required: true }],
            returnTotal: { type: Number, required: true },
            exchangedOn: { type: Date, required: true, default: Date.now },
        },
        required: false,
    })
    returnDetails?: {
        returnPurchaseIds: string[];
        returnTotal: number;
        exchangedOn?: Date;
    };

    @Prop({
        type: {
            returnPurchaseIds: [{ type: SchemaTypes.ObjectId, ref: 'invoices', required: true }],
            returnTotal: { type: Number, required: true },
            exchangedOn: { type: Date, required: true, default: Date.now },
        },
        required: false,
        default: null
    })
    returnCustomPackageDetails?: {
        returnPurchaseIds: string[];
        returnTotal: number;
        exchangedOn?: Date;
    };

    @Prop({
        type: Boolean,
        default: false,
        required: false,
    })
    isForBusiness: boolean;

    @Prop({
        type: SchemaTypes.ObjectId,
        default: null,
        required: false,
        ref: 'User'
    })
    paymentBy: string
}
export const InvoiceSchema = SchemaFactory.createForClass(Invoice);