import { Injectable } from '@nestjs/common';
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";

import { MailService } from "src/mail/services/mail.service";
import { UploadService } from "src/utils/services/upload.service";
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import { Invoice } from "../schemas/invoice.schema";
import { DiscountType } from 'src/utils/enums/discount.enum';
import { User } from "src/users/schemas/user.schema";
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

import sharp from 'sharp';
import axios from 'axios';
import { Purchase } from 'src/users/schemas/purchased-packages.schema';
const numberToWords = require('number-to-words');

@Injectable()
export class InvoiceService {
    constructor(
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<User>,

        private readonly mailerService: MailService,
        private UploadService: UploadService,

    ) { }
    convertAmountToWords(amount) {
        const amountInWords = numberToWords.toWords(amount);
        return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
    }

    async generateInvoice(invoiceData: any, orgEmail: String) {
        try {
            let data = invoiceData.toObject ? invoiceData.toObject() : invoiceData;
            // Step 1: Process the invoice data
            const formattedDate = new Date(data.invoiceDate).toDateString();
            data.invoiceDate = formattedDate.split(' ').slice(1).join(' ');
            data.purchaseItems = data.purchaseItems.map((item, index) => ({
                ...item,
                index: index + 1, // Start index from 1
            }));
            data.productItem = data.productItem.map((item, index) => ({
                ...item,
                index: data.purchaseItems.length + index + 1, // Continue index after purchaseItems
            }));
            data.customPackageItems = data.customPackageItems.map((item, index) => ({
                ...item,
                index: data.customPackageItems.length + index + 1,
            }));
            data.cartDiscountAmount = Number(data.cartDiscountAmount || 0).toFixed(2);
            data.itemDiscount = Number(data.itemDiscount || 0).toFixed(2);
            data.returnDiscount = Number(data.returnDiscount || 0).toFixed(2); 

            const clientBillingUTCode = data?.clientBillingDetails?.utCode;
            const billingUTCode = data?.billingDetails?.utCode;
            const totalGSTValue = data?.totalGstValue;

            if (clientBillingUTCode === billingUTCode) {
                data.cgst = totalGSTValue / 2;
                data.sgst = totalGSTValue / 2;
                data.igst = 0;
            } else {
                data.igst = totalGSTValue;
                data.cgst = 0;
                data.sgst = 0;
            }
            // Step 2: Load and compile the Handlebars template
            const paths = path.join(process.cwd(), "templates", "invoices.hbs");
            const template = fs.readFileSync(paths, "utf-8");
            const Handlebars = require("handlebars");
            const options = {
                allowProtoPropertiesByDefault: true,
                allowProtoMethodsByDefault: true
            };
            Handlebars.registerHelper("eq", (a, b) => a === b);
            Handlebars.registerHelper("inc", function (value) {
                return parseInt(value) + 1;
            });
            Handlebars.registerHelper('eq', function (a, b) {
                return a === b;
            });
            Handlebars.registerHelper('showZero', function (value) {
                return value !== undefined && value !== null ? value : 0;
            });
            // Register a helper for "not equal"
            Handlebars.registerHelper('neq', function (a, b) {
                return a !== b;
            });
            const compiledTemplate = Handlebars.compile(template, options);
            // Step 3: Generate HTML and PDF
            const html = compiledTemplate(data);
            const browser = await puppeteer.launch({
                args: ['--no-sandbox', '--disable-setuid-sandbox'],
            });
            const page = await browser.newPage();
            await page.setContent(html);
            const pdfBuffer = Buffer.from(await page.pdf({ format: "A4" }));
            await browser.close();

            // // Save PDF buffer to local file
            // const localFilePath = path.join(process.cwd(), 'temp', `invoice-${data.invoiceNumber}.pdf`);
            // // Ensure temp directory exists
            // if (!fs.existsSync(path.join(process.cwd(), 'temp'))) {
            //     fs.mkdirSync(path.join(process.cwd(), 'temp'));
            // }
            // fs.writeFileSync(localFilePath, pdfBuffer);

            // Step 4: Upload the PDF to S3
            const s3Path = "invoices/";
            const fileName = `invoice-${data.orderId}.pdf`;
            const s3UploadResponse = await this.UploadService.uploadPdf(pdfBuffer, s3Path, fileName, "application/pdf");

            // Step 5: Update invoice record with file path
            await this.InvoiceModel.findOneAndUpdate(
                { _id: data._id },
                { invoiceFilePath: s3UploadResponse.Location },
                { new: true, upsert: true }
            );
            const staffsList = await this.getALltheStaffs(data?.facilityId);
           


            const qrAttachments = [];

            for (const item of data.purchaseItems || []) {
                const purchaseIds = item.purchaseIds || [];

                for (const purchaseId of purchaseIds) {
                    const purchase: Purchase = await this.PurchaseModel.findById(purchaseId).lean();

                    if (purchase?.qrCodeUrl) {
                    try {
                        const response = await axios.get(purchase.qrCodeUrl.toString(), { responseType: 'arraybuffer' });
                        const qrBuffer = Buffer.from(response.data);

                        const canvasWidth = 300;
                        const canvasHeight = 300;
                        const qrSize = 200;

                        const topText = item.packageName.toUpperCase() || 'Package';

                        const svgOverlay = Buffer.from(`
                        <svg width="${canvasWidth}" height="${canvasHeight}">
                            <style>
                            .title {
                                fill: #000;
                                font-size: 20px;
                                font-weight: bold;
                                font-family: Arial, sans-serif;
                                dominant-baseline: middle;
                            }
                            </style>
                            <text x="50%" y="30" text-anchor="middle" class="title">${topText}</text>
                        </svg>
                        `);

                        const resizedQRBuffer = await sharp(qrBuffer)
                        .resize(qrSize, qrSize)
                        .toBuffer();

                        const finalImage = await sharp({
                        create: {
                            width: canvasWidth,
                            height: canvasHeight,
                            channels: 4,
                            background: { r: 255, g: 255, b: 255, alpha: 1 },
                        }
                        })
                        .composite([
                        { input: svgOverlay, top: 0, left: 0 },
                        { input: resizedQRBuffer, top: 60, left: (canvasWidth - qrSize) / 2 }
                        ])
                        .png()
                        .toBuffer();
                        qrAttachments.push({
                        filename: `${topText.replace(/\s+/g, '_')}_QR.png`,
                        content: finalImage,
                        contentType: 'image/png',
                        });

                    } catch (error) {
                        console.error(`❌ Failed to process QR for purchase ${purchaseId}:`, error);
                    }
                    }
                }
                }


             //Step 6: Send email with invoice attachment
            await this.mailerService.sendMail({
                to: data?.clientDetails?.email.toString(),
                subject: `Invoice #${data.invoiceNumber}`,
                template: "invoice-body",
                context: data,
                attachments: [
                    {
                        filename: `invoice-${data.invoiceNumber}.pdf`,
                        path: s3UploadResponse.Location,
                    },
                    ...qrAttachments
                ],
            });

            /* Send mail to the organization about the package sold to which client */
            await this.mailerService.sendMail({
                to: orgEmail?.toString(),
                subject: `Package Sold`,
                template: "package-sold",
                context: data,
                attachments: [
                    {
                        filename: `invoice-${data.invoiceNumber}.pdf`,
                        path: s3UploadResponse.Location,
                    },
                    ...qrAttachments
                ],
            });
            for (const staff of staffsList) {
                await this.mailerService.sendMail({
                    to: staff.email.toString(),
                    subject: `Package Sold`,
                    template: "package-sold",
                    context: data,
                    attachments: [
                        {
                            filename: `invoice-${data.invoiceNumber}.pdf`,
                            path: s3UploadResponse.Location,
                        },
                    ],
                });
            }
            return { message: "Invoice email sent successfully!" };
        } catch (error) {
            console.error("Error in generateInvoice function:", error);
            return {
                message: "Error generating invoice",
                details: error.message
            };
        }
    }
    private async getALltheStaffs(facilityId: Types.ObjectId): Promise<any[]> {
        const commonPipeline: any[] = [
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffDetails",
                    pipeline: [{ $match: { facilityId: { $in: [facilityId] } } }],
                },
            },
            { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: 'role',
                    localField: "role",
                    foreignField: "_id",
                    as: "roleDetails",
                    pipeline: [
                        {
                            $match: {
                                type: { $in: [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER] },
                            },
                        },
                    ],
                }
            },
            { $unwind: { path: "$roleDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "staffDetails.facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
        ];
        commonPipeline.push({
            $group: {
                _id: "$staffDetails._id",
                gender: { $first: "$staffDetails.gender" },
                profilePicture: { $first: "$staffDetails.profilePicture" },
                userId: { $first: "$_id" },
                firstName: { $first: "$firstName" },
                lastName: { $first: "$lastName" },
                mobile: { $first: "$mobile" },
                email: { $first: "$email" },
                role: { $first: "$role" },
                isActive: { $first: "$isActive" },
                createdAt: { $first: "$createdAt" },
                facilityNames: { $push: "$facilityDetails.facilityName" },
            },
        });
        commonPipeline.push(
            {
                $sort: {
                    isActive: -1,
                    updatedAt: -1,
                },
            },
            {
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        {
                            $project: {
                                _id: 1,
                                userId: 1,
                                firstName: 1,
                                lastName: 1,
                                facilityNames: 1,
                                mobile: 1,
                                email: 1,
                                role: 1,
                                isActive: 1,
                                createdAt: 1,
                                gender: 1,
                                profilePicture: 1,
                            },
                        },

                    ],
                },
            },
        );

        // Execute aggregation
        const result = await this.UserModel.aggregate(commonPipeline);
        return result[0]?.data || []

    }



    async sendPendingPaymentEmailWithQR(invoice: any, link: string, qrBuffer: Buffer, toEmail: string) {
        const subject = `Complete Your Payment for Invoice #${invoice.invoiceNumber}`;

        const attachments = [{
            filename: `invoice-${invoice.invoiceNumber}-qr.png`,
            content: qrBuffer,
            contentType: 'image/png',
            cid: 'qrcode@inline' // for inline use
        }];

        const html = `
            <p>Hi,</p>
            <p>Your invoice <strong>#${invoice.invoiceNumber}</strong> is pending.</p>
            <p>Please click the link below to pay:</p>
            <p><a href="${link}" target="_blank">${link}</a></p>
            <p>Or scan this QR code using any UPI app:</p>
            <img src="cid:qrcode@inline" alt="Pay QR Code" width="200" />
        `;

        await this.mailerService.sendMail({
            to: toEmail,
            subject,
            attachments,
            html
        });
    }




    async sendPendingInvoicePaymentEmailWithQR(
        invoiceData: any,
        qrBuffer: Buffer,
        paymentLinkUrl: string
    ) {
        try {
            const data = invoiceData.toObject ? invoiceData.toObject() : invoiceData;

            // Format invoice date
            const formattedDate = new Date(data.invoiceDate).toDateString();
            data.invoiceDate = formattedDate.split(" ").slice(1).join(" "); // e.g., "Jul 18 2025"

            // Add index to each item type
            let runningIndex = 1;

            data.purchaseItems = (data.purchaseItems || []).map((item) => ({
                ...item,
                index: runningIndex++,
            }));

            data.productItem = (data.productItem || []).map((item) => ({
                ...item,
                index: runningIndex++,
            }));

            data.customPackageItems = (data.customPackageItems || []).map((item) => ({
                ...item,
                index: runningIndex++,
            }));

            // GST Breakdown
            const clientUT = data?.clientBillingDetails?.utCode;
            const orgUT = data?.billingDetails?.utCode;
            const totalGST = data?.totalGstValue || 0;

            if (clientUT && orgUT && clientUT === orgUT) {
                data.cgst = totalGST / 2;
                data.sgst = totalGST / 2;
                data.igst = 0;
            } else {
                data.igst = totalGST;
                data.cgst = 0;
                data.sgst = 0;
            }

            // Add QR and payment link
            data.paymentLinkUrl = paymentLinkUrl;

            // Load and compile Handlebars template
            const templatePath = path.join(process.cwd(), "templates", "pending-invoice-payment.hbs");
            const rawTemplate = fs.readFileSync(templatePath, "utf-8");

            const Handlebars = require("handlebars");
            const options = {
                allowProtoPropertiesByDefault: true,
                allowProtoMethodsByDefault: true,
            };

            // Register helpers
            Handlebars.registerHelper("eq", (a, b) => a === b);
            Handlebars.registerHelper("neq", (a, b) => a !== b);
            Handlebars.registerHelper("inc", (value) => parseInt(value) + 1);
            Handlebars.registerHelper("showZero", (value) =>
                value !== undefined && value !== null ? value : 0
            );

            const compiledTemplate = Handlebars.compile(rawTemplate, options);
            const html = compiledTemplate(data);

            // Send the email to the client only
            const clientEmail = data?.clientDetails?.email?.toString();
            if (!clientEmail) {
                throw new Error("Client email is missing from invoice data.");
            }

            await this.mailerService.sendMail({
                to: clientEmail,
                subject: `Pending Invoice Payment - ${data.invoiceNumber}`,
                html,
                attachments: [
                    {
                        filename: "payment-qr-code.png",
                        content: qrBuffer,
                        cid: "qrCodeCid" // Embed it inline using cid
                    },
                ],
            });

            return { message: "Pending invoice email sent successfully!" };
        } catch (error) {
            console.error("Error in sendPendingInvoicePaymentEmailWithQR:", error);
            return {
                message: "Error sending pending invoice email",
                details: error.message,
            };
        }
    }



}
