import { Global, Module, forwardRef } from "@nestjs/common";
import { UserController } from "./controllers/user.controller";
import { UserService } from "./services/user.service";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "./schemas/user.schema";
import { UtilsModule } from "src/utils/utils.module";
import { AuthModule } from "src/auth/auth.module";
import { ClientProfileDetails, ClientProfileSchema } from "./schemas/user-profile-details.schema";
import { AdminUserController } from "./controllers/admin-user.controller";
import { ClientsController } from "./controllers/clients.controller";
import { ClientsService } from "./services/clients.service";
import { Clients, ClientSchema } from "./schemas/clients.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { UsersPipe } from "./pipes/users.pipe";
import { MailModule } from "src/mail/mail.module";
import { PurchaseController } from "./controllers/purchase.controller";
import { PurchaseService } from "./services/purchase.service";
import { Purchase, PurchaseSchema } from "./schemas/purchased-packages.schema";
import { Pricing, PricingSchema } from "src/organization/schemas/pricing.schema";
import { Invoice, InvoiceSchema } from "./schemas/invoice.schema";
import { membership, MembershipSchema } from "src/membership/schema/membership.schema";
import { InvoiceService } from "./services/invoice.service"
import { Scheduling, SchedulingSchema } from "src/scheduling/schemas/scheduling.schema";
import { PayRate, PayRateSchema } from "src/staff/schemas/pay-rate.schema";
import { Inventory, InventorySchema } from "src/merchandise/schema/inventory.schema";
import { Product, ProductSchema } from "src/merchandise/schema/product.schema";
import { ProductVariant, ProductVariantSchema } from "src/merchandise/schema/product-variant.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { SessionModule } from "src/session/session.module";
import { CustomPackage, CustomPackageSchema } from "src/customPackage/schemas/custom-package.schema";
import { CartModule } from "src/cart/cart.module";
import { RoleModule } from "src/role/role.module";
import { Reconciliation, ReconciliationSchema } from "src/transactions/schema/reconciliation.schema";
import { PaymentMethod, PaymentMethodSchema } from "src/paymentMethod/schemas/payment-method.schema";
import { Services, ServiceSchema } from "src/organization/schemas/services.schema";
import { ClientLead, ClientLeadSchema } from "src/zoho-webhook/schema/zoho-webhook.schema";
import { PricingSessionLogs, PricingSessionLogsSchema } from "./schemas/pricingSessions.log";
import { Enrollment, EnrollmentSchema } from "src/courses/schemas/enrollment.schema";
import { PaymentModule } from "src/payment/payment.module";
import { PurchasePublicController } from "./controllers/purchase.public.controller";
import { SchedulingService } from "src/scheduling/services/scheduling.service";
import { SchedulingModule } from "src/scheduling/scheduling.module";
import { Organizations, OrganizationSchema } from "src/organization/schemas/organization.schema";

@Global()
@Module({
    imports: [
        forwardRef(() => AuthModule),
        forwardRef(() => SessionModule),
        forwardRef(() => SchedulingModule),
        UtilsModule,
        MailModule,
        CartModule,
        PaymentModule,
        RoleModule,
        SchedulingModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: ClientProfileDetails.name, schema: ClientProfileSchema },
            { name: Purchase.name, schema: PurchaseSchema },
            { name: Pricing.name, schema: PricingSchema },
            { name: Invoice.name, schema: InvoiceSchema },
            { name: membership.name, schema: MembershipSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: Inventory.name, schema: InventorySchema },
            { name: Product.name, schema: ProductSchema },
            { name: ProductVariant.name, schema: ProductVariantSchema },
            { name: CustomPackage.name, schema: CustomPackageSchema },
            { name: Reconciliation.name, schema: ReconciliationSchema },
            { name: PaymentMethod.name, schema: PaymentMethodSchema },
            { name: Services.name, schema: ServiceSchema },
            { name: ClientLead.name, schema: ClientLeadSchema },
            { name: PricingSessionLogs.name, schema: PricingSessionLogsSchema },
            { name: Enrollment.name, schema: EnrollmentSchema },
            { name: Organizations.name, schema: OrganizationSchema },

        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [UserController, AdminUserController, ClientsController, PurchaseController, PurchasePublicController],
    providers: [UserService, ClientsService, UsersPipe, PurchaseService, InvoiceService,],
    exports: [
        UserService,
        PurchaseService,
        InvoiceService,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Pricing.name, schema: PricingSchema },
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
})
export class UsersModule { }
