import { BadRequestException, Body, Controller, UseGuards, Get, HttpCode, Param, Patch, Post, Headers, StreamableFile, UploadedFile, ParseFilePipe, FileTypeValidator, UseInterceptors } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { InventoryProductFilterDto } from "../dto/inventoryFilterProduct.dto";
import { InventoryService } from "../services/inventory.service";
import { ProductType } from "src/merchandise/schema/product.schema";
import { CreateInventoryDto } from "../dto/createInventory.dto";
import { FilterInventoryDto } from "../dto/filterInventory.dto";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { ExportInventoryDto } from "../dto/exportInventory.dto";
import { ExportService } from "src/utils/services/export.service";
import { InventoryExportService } from "src/utils/services/inventory-export.service";
import { CsvParserService } from "src/utils/services/csv-parser.service";
import { FileInterceptor } from "@nestjs/platform-express";
import { ExportInventoryTemplateDto } from "../dto/export-inventory-template.dto";
import { InventoryTemplateExportService } from "src/utils/services/inventory-template-export.service";
@ApiTags("inventory")
@ApiBearerAuth()
@Controller("inventory")
export class InventoryController {
    constructor(private inventoryService: InventoryService,
        private readonly exportService: ExportService,
        private readonly inventoryExportServcie: InventoryExportService,
        private readonly csvParserService: CsvParserService,
        private readonly inventoryTemplateExportService:InventoryTemplateExportService

    ) { }

    @Post("/product/sku_search")
    @ApiOperation({ summary: "search Sku of the Product" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getProductBySku(@Body() FilterProductDto: InventoryProductFilterDto, @GetUser() user: any): Promise<{ message: string; data: any }> {
        let result;
        let productType = FilterProductDto.productType;
        switch (productType) {
            case ProductType.SIMPLE:
                result = await this.inventoryService.getSimpleProducts(FilterProductDto, user);
                break;
            case ProductType.VARIABLE:
                result = await this.inventoryService.getVariableProducts(FilterProductDto, user);
                break;
            default:
                throw new BadRequestException("Invalid product type");
        }
        return { message: "Product details fetched successfully", data: result };
    }

    @Post("/create")
    @ApiOperation({ summary: "Create a new Product" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async createInventory(@Body() createInventoryDto: CreateInventoryDto, @GetUser() user: any): Promise<{ message: string; data: any }> {
        let result;
        result = await this.inventoryService.createInventory(createInventoryDto, user);
        return { message: "Product details fetched successfully", data: result };

    }

    @Post("/")
    @ApiOperation({ summary: "List of Inventory" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getInventoryList(@Body() FilterInventoryDto: FilterInventoryDto, @GetUser() user: any): Promise<{ message: string; data: any }> {
        return { message: "Inventory list fetched successfully", data: await this.inventoryService.getInventoryList(FilterInventoryDto, user) };
    }

    @Patch("/:id")
    @ApiOperation({ summary: "Update Inventory Details" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async updateInventoryDetails(
        @Param("id") id: string,
        @Body() CreateInventoryDto: CreateInventoryDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<{ message: string; data: any }> {
        return { message: "Inventory details updated successfully", data: await this.inventoryService.updateInventoryDetails(id, organizationId, CreateInventoryDto) };
    }

    @Post("/store-inventory")
    @ApiOperation({ summary: "List of Inventory on the basis of Store" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async storeInventoryList(
        @Body() body: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        const result = await this.inventoryService.organizationInventoryList(body, organizationId);
        return {
            message: "Inventory of the Store  Fetched successfully!",
            data: result,
        };
    }
    @Post('/export')
    @HttpCode(200)
    @ApiOperation({ summary: 'Export Inventory' })
    @PolicyAbilityRoleProtected(
        ENUM_ROLE_TYPE.ORGANIZATION,
        ENUM_ROLE_TYPE.WEB_MASTER,
        ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
        ENUM_ROLE_TYPE.TRAINER
    )
    @AuthJwtAccessProtected()
    async exportInventoryList(
        @Headers('X-Timezone') userTimezone: string,
        @GetUser() user: any,
        @Body() body: ExportInventoryDto
    ): Promise<any> {
        userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
        // ⬇️ Service will enforce organization AND optional facilityId
        const rows = await this.inventoryService.getInventoryForExport(user, body);
        if (!rows.length) {
            return { message: 'No inventory found to export', data: [] };
        }

        const fileType = body.fileType || 'csv';
        const buffer = await this.inventoryTemplateExportService.generateExportFile(rows, fileType);

        if (body.responseType === 'stream') {
            const contentType = this.inventoryTemplateExportService.getMimeType(fileType);
            const fileName = `inventory-${Date.now()}.${fileType}`;
            return new StreamableFile(new Uint8Array(buffer), {
                type: contentType,
                disposition: `attachment; filename=${fileName}`,
            });
        }
        return { message: 'Inventory fetched successfully', data: rows };
    }
    @Post('/bulkUpload')
    @UseInterceptors(FileInterceptor('inventoryFile'))
    @ApiOperation({ summary: 'Bulk Upload Inventory (CSV)' })
    @PolicyAbilityRoleProtected(
        ENUM_ROLE_TYPE.ORGANIZATION,
        ENUM_ROLE_TYPE.WEB_MASTER,
        ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
        ENUM_ROLE_TYPE.TRAINER
    )
    @AuthJwtAccessProtected()
    async bulkInventoryUpload(
        @Body() body: any,
        @UploadedFile(
            new ParseFilePipe({
                validators: [new FileTypeValidator({ fileType: '.(csv)' })],
            }),
        )
        inventoryFile: Express.Multer.File,
        @GetUser() user: any,
    ): Promise<{ message: string; uploadReport?: any }> {
        if (!inventoryFile?.buffer?.length) {
            throw new BadRequestException('Unable to upload inventory / Empty file uploaded');
        }

        // Reuse your CSV parser
        const fileBuffer = inventoryFile.buffer;
        const jsonData = await this.csvParserService.parseCsv(fileBuffer);
        if (!jsonData?.length) {
            throw new BadRequestException('Unable to upload inventory / Empty file uploaded');
        }
        const isUpdate = body.isupdate === true || body.isupdate === 'true';


        const uploadReport = await this.inventoryService.bulkUploadInventory(jsonData, user,
            body.storeId,
        
        );

        return {
            message: 'Bulk inventory upload is complete',
            uploadReport,
        };
    }
 @Post('/export-products-template')
@HttpCode(200)
@ApiOperation({
  summary: 'Export all products (simple + variable) in inventory-import CSV/XLSX format',
})
@PolicyAbilityRoleProtected(
  ENUM_ROLE_TYPE.ORGANIZATION,
  ENUM_ROLE_TYPE.WEB_MASTER,
  ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
  ENUM_ROLE_TYPE.TRAINER
)
@AuthJwtAccessProtected()
async exportProductsTemplate(
  @Headers('X-Timezone') userTimezone: string,
  @GetUser() user: any,
  @Body() body: ExportInventoryTemplateDto, // fileType: 'csv'|'xlsx', responseType?: 'stream'|'json', storeId?: string
): Promise<any> {
  userTimezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;

  // Service returns rows shaped exactly like your inventory import CSV:
  // productName, variantTitle, productSku, productType, variantSku, mrp, salePrice, quantity, expiryDate
  // If storeId is provided, quantity/salePrice/mrp/expiryDate are prefilling from that store’s inventory when available.
  const rows = await this.inventoryService.getProductsAsInventoryTemplate(user);

  if (!rows.length) {
    return { message: 'No products found to export', data: [] };
  }

  const fileType = body.fileType || 'csv';
  const buffer = await this.inventoryTemplateExportService.generateExportFile(rows, fileType); // csv/xlsx only

  // default to stream (download) unless explicitly requested otherwise
  const responseType = body.responseType || 'stream';
  if (responseType === 'stream') {
    const contentType = this.inventoryTemplateExportService.getMimeType(fileType);
    const fileName = `inventory_template-${Date.now()}.${fileType}`;
    return new StreamableFile(new Uint8Array(buffer), {
      type: contentType,
      disposition: `attachment; filename=${fileName}`,
    });
  }

  return { message: 'Products template fetched successfully', data: rows };
}

}
