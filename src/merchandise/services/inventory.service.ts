import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { ProductVariant } from "src/merchandise/schema/product-variant.schema";
import { Product, ProductType } from "src/merchandise/schema/product.schema";
import { InventoryProductFilterDto } from "../dto/inventoryFilterProduct.dto";
import { TransactionService } from "src/utils/services/transaction.service";
import { Facility } from "src/facility/schemas/facility.schema";
import { Inventory } from "../schema/inventory.schema";
import { CreateInventoryDto } from "../dto/createInventory.dto";
import { FilterInventoryDto } from "../dto/filterInventory.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { ID } from "aws-sdk/clients/s3";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { PromotionService } from "src/promotions/services/promotion.service";
import { DiscountType } from "src/utils/enums/discount.enum";
import { ENUM_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { PromotionItemService } from "src/promotions/services/promotion-items.service";
import { ExportInventoryDto } from "../dto/exportInventory.dto";

const mongoose = require("mongoose");
const isValidId = (id?: string) => !!id && Types.ObjectId.isValid(id);
const oid = (id: string) => new Types.ObjectId(id);

interface BulkUploadOptions {
    storeId: string;
}
interface RowIssue {
    row: number;
    field?: string;
    code: string;
    message: string;
    sku?: string;
}
type TemplateRow = {
    productName: string;
    variantTitle: string;                      // '' for simple
    productSku: string;
    productType: 'simple' | 'variable';
    variantSku: string;                        // '' for simple
    mrp: number | '';
    salePrice: number | '';
    quantity: number | '';
    expiryDate: string;                        // YYYY-MM-DD or ''
};
interface UploadReport {
    summary: {
        totalRows: number;
        validRows: number;
        invalidRows: number;
        inserted: number;
        updated: number;
        // skipped: number;
    };
    errors: RowIssue[];
    warnings: RowIssue[];
}
@Injectable()
export class InventoryService {
    constructor(
        @InjectModel(Product.name) private readonly productModel: Model<Product>,
        @InjectModel(ProductVariant.name) private readonly productVariantModel: Model<ProductVariant>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Inventory.name) private inventoryModel: Model<Inventory>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,

        private readonly promotionItemService: PromotionItemService,
        private readonly promotionService: PromotionService,
        private readonly transactionService: TransactionService,
    ) { }

    async getSimpleProducts(filterProductDto: InventoryProductFilterDto, user) {
        let organizationId = await this.getOrganizationId(user)
        const pageSize = filterProductDto.pageSize ?? 30;
        const page = filterProductDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let query: any = { status: true };
        query["organizationId"] = organizationId
        if (filterProductDto.productType) {
            query["type"] = filterProductDto.productType;
        }

        if (filterProductDto.search?.trim()) {
            const titleQueryString = filterProductDto.search.trim().split(" ").join("|");
            query["$or"] = [
                { sku: { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                { name: { $regex: `.*${titleQueryString}.*`, $options: "i" } } // Added name field for search
            ];
        }

        const data = await this.productModel.aggregate([
            { $match: query },
            { $skip: skip },
            { $limit: pageSize },
            {
                $project: {
                    name: 1,
                    sku: 1,
                    hsnCode: 1,
                    gst: 1,
                },
            },
        ]);

        return data;
    }

    async getVariableProducts(filterProductDto: InventoryProductFilterDto, user) {
        const organizationId = await this.getOrganizationId(user)
        const pageSize = filterProductDto.pageSize ?? 30;
        const page = filterProductDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let query: any = { status: true };
        if (filterProductDto.search) {
            const titleQueryString = filterProductDto.search.trim().split(" ").join("|");
            query["$or"] = [{ sku: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
        }
        query["organizationId"] = organizationId
        // let data = await this.productVariantModel.find(query, { name: "$title", sku: 1, hsnCode: 1 }).skip(skip).limit(page).populate("productId", "id name gst").exec();
        let data = await this.productVariantModel.aggregate([
            {
                $match: query,
            },
            {
                $skip: skip,
            },
            {
                $limit: pageSize,
            },
            {
                $lookup: {
                    from: "products",
                    localField: "productId",
                    foreignField: "_id",
                    as: "productData",
                },
            },
            {
                $unwind: {
                    path: "$productData",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $project: {
                    name: "$title",
                    sku: 1,
                    hsnCode: 1,
                    gst: "$productData.gst",
                    productId: 1,
                    type: "$productData.type",
                },
            },
        ]);
        return data;
    }
    async createInventory(createInventoryDto: CreateInventoryDto, user: any) {
        const session = await this.transactionService.startTransaction();
        try {
            let organizationId = await this.getOrganizationId(user)
            const { storeId, promotionId, applyPromotion, ...inventoryDetails } = createInventoryDto;
            const store = await this.FacilityModel.findById(storeId).exec();
            let isProductExist
            if (!store) {
                throw new NotFoundException("Store not found");
            }
            if (inventoryDetails.productType === 'variable') {
                isProductExist = await this.inventoryModel.findOne({ storeId: storeId, organizationId: organizationId, productVariantId: inventoryDetails?.productVariantId })
            }
            if (inventoryDetails.productType === 'simple') {
                isProductExist = await this.inventoryModel.findOne({ storeId: storeId, organizationId: organizationId, productId: inventoryDetails?.productId })
            } if (isProductExist) {
                throw new NotFoundException("Product Already Exist in store Inventory");

            }
            if (inventoryDetails.productType === ProductType.SIMPLE) {
                const inventory = await this.inventoryModel.findOne({ productId: inventoryDetails?.productId, productType: ProductType.SIMPLE });

                const existProd = await this.productModel.findOne({ _id: inventoryDetails?.productId, type: ProductType.SIMPLE });
                if (!existProd) {
                    throw new BadRequestException("Invalid product id");
                }
                if (!inventory) {
                    await this.productModel.findByIdAndUpdate(inventoryDetails?.productId, { status: true }, { new: true, session });
                }
                delete inventoryDetails["productVariantId"];
            }

            if (inventoryDetails.productType === ProductType.VARIABLE) {
                const inventory = await this.inventoryModel.findOne({
                    productId: inventoryDetails.productId,
                    productVariantId: inventoryDetails.productVariantId,
                    productType: ProductType.VARIABLE,
                });

                const variantExist = await this.productVariantModel.findOne({ _id: inventoryDetails.productVariantId, productId: inventoryDetails?.productId });
                if (!variantExist) {
                    throw new BadRequestException("Invalid product and product variant id");
                }
                if (!inventory) {
                    const proInv = await this.productModel.findByIdAndUpdate(inventoryDetails?.productId, { status: true }, { new: true, session });
                    const proVarInv = await this.productVariantModel.findByIdAndUpdate(inventoryDetails.productVariantId, { status: true }, { new: true, session });
                }
            }

            if (promotionId) {
                const promotion = await this.promotionService.findOneById(new Types.ObjectId(promotionId));
                await this.promotionService.validateItemDiscount(promotion, inventoryDetails.mrp);
            }

            let data: Inventory = {
                ...inventoryDetails,
                promotion: promotionId ? new Types.ObjectId(promotionId) : null,
                storeId: store._id,
                organizationId
            };
            const saveData = await new this.inventoryModel(data)

            if (applyPromotion?.length > 0) {
                // Validate all promotions in bulk
                const promotionIds = applyPromotion.map(id => new Types.ObjectId(id));
                await Promise.all(promotionIds.map(promotionId =>
                    this.validateDiscount(promotionId, new Types.ObjectId(organizationId), inventoryDetails.mrp)
                ));
                await this.promotionService.applyPromotionsToItem(
                    new Types.ObjectId(organizationId),
                    {
                        applyPromotions: applyPromotion,
                    },
                    {
                        itemType: ENUM_ITEM_TYPE.SERVICE,
                        _id: new Types.ObjectId(saveData._id), // Use the newly created pricing's ID,
                        price: saveData.mrp as number
                    },
                    { session }
                );
            }


            await saveData.save({ session });
            await this.transactionService.commitTransaction(session);
            return saveData;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getInventoryList(filterInventoryDto: FilterInventoryDto, user: any) {
        let organizationId = await this.getOrganizationId(user)
        const { search, page = 1, pageSize = 10, storeId, productType } = filterInventoryDto;
        const skip = pageSize * (page - 1);

        let query: any = { organizationId };

        if (storeId && mongoose.Types.ObjectId.isValid(storeId)) {
            query.storeId = new mongoose.Types.ObjectId(storeId);
        }
        if (productType) {
            query.productType = productType;
        }
        // Creating a search query string with regex pattern
        let searchQuery: any = {};
        if (search) {
            const titleQueryString = search.trim().split(" ").join("|");
            searchQuery = {
                $or: [
                    { "productDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },

                ]
            };
        }

        let countProm = this.inventoryModel.countDocuments({ ...query, ...searchQuery });

        let dataProm = this.inventoryModel
            .aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "products",
                        localField: "productId",
                        foreignField: "_id",
                        as: "productDetails",
                        pipeline: [
                            { $project: { name: 1, sku: 1, gst: 1, hsn: 1 } }
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "productvariants",
                        localField: "productVariantId",
                        foreignField: "_id",
                        as: "productVariantDetails",
                        pipeline: [
                            { $project: { name: "$title", sku: 1, gst: 1, hsnCode: 1 } }
                        ],
                    },
                },
                { $unwind: { path: "$productDetails", preserveNullAndEmptyArrays: true } },
                { $unwind: { path: "$productVariantDetails", preserveNullAndEmptyArrays: true } },
                { $match: searchQuery },
                { $sort: { createdAt: -1 } },
                { $skip: skip },
                { $limit: pageSize }
            ]);

        let [count, list] = await Promise.all([countProm, dataProm.exec()]);
        return { count, list };
    }
    async updateInventoryDetails(id: string, organizationId: IDatabaseObjectId, createInventoryDto: CreateInventoryDto) {
        const session = await this.transactionService.startTransaction();
        try {
            const { storeId, productId, productVariantId, promotionId, applyPromotion, ...inventoryDetails } = createInventoryDto;
            const store = await this.FacilityModel.findById(storeId).exec();

            if (!store) {
                throw new NotFoundException("Store not found");
            }

            if (inventoryDetails.productType === ProductType.SIMPLE) {
                const existProd = await this.productModel.findOne({ _id: productId, type: ProductType.SIMPLE });
                if (!existProd) {
                    throw new NotFoundException("Product not found");
                }
                const inventory = await this.inventoryModel.findOne({ _id: id, productType: ProductType.SIMPLE });

                if (!inventory) {
                    throw new NotFoundException("Inventory not found");
                }
            }

            if (inventoryDetails.productType === ProductType.VARIABLE) {
                const variantExist = await this.productVariantModel.findOne({ _id: productVariantId, productId });
                if (!variantExist) {
                    throw new NotFoundException("Product Variant not found");
                }
                const inventory = await this.inventoryModel.findOne({ _id: id, productType: ProductType.VARIABLE });

                if (!inventory) {
                    throw new NotFoundException("Inventory not found");
                }
            }


            if (promotionId) {
                await this.validateDiscount(new Types.ObjectId(promotionId), organizationId, inventoryDetails.mrp as number);
                const isExist = await this.promotionItemService.getTotal({
                    promotion: promotionId,
                    item: new Types.ObjectId(id)
                });
                if (!isExist) {
                    await this.promotionItemService.create({
                        organizationId: new Types.ObjectId(organizationId),
                        promotion: promotionId,
                        itemType: ENUM_ITEM_TYPE.PRODUCT,
                        item: new Types.ObjectId(id)
                    }, { session });
                }
            }

            if (applyPromotion?.length > 0) {
                // Validate all promotions in bulk
                const promotionIds = applyPromotion.map(id => new Types.ObjectId(id));
                await Promise.all(promotionIds.map(promotionId =>
                    this.validateDiscount(promotionId, new Types.ObjectId(organizationId), inventoryDetails.mrp as number)
                ));
                await this.promotionService.applyPromotionsToItem(
                    new Types.ObjectId(organizationId),
                    {
                        applyPromotions: applyPromotion,
                    },
                    {
                        itemType: ENUM_ITEM_TYPE.PRODUCT,
                        _id: new Types.ObjectId(id),
                        price: inventoryDetails.mrp as number
                    },
                    { session }
                );
            }

            const data: Partial<Inventory> = {
                ...inventoryDetails,
                promotion: promotionId ? new Types.ObjectId(promotionId) : null,
                storeId: store._id
            }

            const saveData = await this.inventoryModel.findByIdAndUpdate(id, data, { session }).exec();

            await this.transactionService.commitTransaction(session);
            return saveData;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    private async getOrganizationId(user: any) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientsModel.findOne({ userId: user.id }).exec();
                organizationId = client.organizationId;
                break

            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.WEB_MASTER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        return organizationId;
    }
    async organizationInventoryList(body: any, organizationId: IDatabaseObjectId) {
        const { search } = body;

        /* ****Later we want to add store id on the basis of store select */
        let query = {
            organizationId: organizationId
        };

        // Creating a search query for the aggregation pipeline
        let searchQuery: any = {};
        if (search) {
            const titleQueryString = search.trim().split(" ").join("|");
            searchQuery = {
                $or: [
                    { "productDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.name": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                    { "productVariantDetails.sku": { $regex: `.*${titleQueryString}.*`, $options: "i" } },
                ]
            };
        }

        let countProm = this.inventoryModel.countDocuments({ ...query });

        let dataProm = this.inventoryModel
            .aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "products",
                        localField: "productId",
                        foreignField: "_id",
                        as: "productDetails",
                        pipeline: [
                            { $project: { name: 1, sku: 1, gst: 1, hsn: 1 } }
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "productvariants",
                        localField: "productVariantId",
                        foreignField: "_id",
                        as: "productVariantDetails",
                        pipeline: [
                            { $project: { name: "$title", sku: 1, gst: 1, hsnCode: 1 } }
                        ],
                    },
                },
                { $unwind: { path: "$productDetails", preserveNullAndEmptyArrays: true } },
                { $unwind: { path: "$productVariantDetails", preserveNullAndEmptyArrays: true } },
                { $match: searchQuery },
                {
                    $sort: {
                        "quantity": -1,
                        createdAt: -1
                    }
                },


            ]);

        let [count, list] = await Promise.all([countProm, dataProm.exec()]);
        return { count, list };
    }

    async validateDiscount(promotionId: IDatabaseObjectId, organizationId: IDatabaseObjectId, itemValue: number) {
        const promotion = await this.promotionService.findOneById(new Types.ObjectId(promotionId));
        if (!promotion) {
            throw new BadRequestException("Invalid promotion id");
        }
        if (promotion.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("Invalid promotion id");
        }
        if (!promotion.isActive || (promotion.endDate && promotion.endDate < new Date())) {
            throw new BadRequestException("Promotion is not active or expired");
        }
        // Check for FLAT discount type
        if (promotion.type === DiscountType.FLAT && (promotion.value < 0 || promotion.value > itemValue)) {
            throw new BadRequestException("Invalid discount value: Flat discount must be between 0 and item value");
        }
        // Check for PERCENTAGE discount type
        if (promotion.type === DiscountType.PERCENTAGE && (promotion.value < 0 || promotion.value > 100)) {
            throw new BadRequestException("Invalid discount value: Percentage discount must be between 0% and 100%");
        }
        return promotion;
    }

    async getInventoryForExport(user: any, dto: ExportInventoryDto): Promise<any[]> {
        let organizationId = await this.getOrganizationId(user)
        if (!isValidId(organizationId)) {
            throw new BadRequestException('User has no valid organizationId');
        }
        const match: Record<string, any> = { organizationId: oid(organizationId) };
        if (dto.facilityId) {
            if (!isValidId(dto.facilityId)) throw new BadRequestException('Invalid facilityId');
            match.storeId = oid(dto.facilityId);
        }
        const pipeline: PipelineStage[] = [
            { $match: match },

            {
                $lookup: {
                    from: 'facilities',
                    let: { sid: '$storeId' },
                    pipeline: [
                        { $match: { $expr: { $eq: ['$_id', '$$sid'] } } },
                        { $project: { _id: 0, name: 1 } },
                        { $limit: 1 },
                    ],
                    as: 'store',
                },
            },

            {
                $lookup: {
                    from: 'products',
                    let: { pid: '$productId' },
                    pipeline: [
                        { $match: { $expr: { $eq: ['$_id', '$$pid'] } } },
                        { $project: { _id: 0, name: 1, sku: 1 } },
                        { $limit: 1 },
                    ],
                    as: 'product',
                },
            },

            {
                $lookup: {
                    from: 'productvariants',
                    let: { vid: '$productVariantId' },
                    pipeline: [
                        { $match: { $expr: { $eq: ['$_id', '$$vid'] } } },
                        { $project: { _id: 0, name: 1, sku: 1 } },
                        { $limit: 1 },
                    ],
                    as: 'variant',
                },
            },

            {
                $set: {
                    _store: { $first: '$store' },
                    _product: { $first: '$product' },
                    _variant: { $first: '$variant' },
                },
            },

            {
                $set: {
                    effectivePrice: {
                        $cond: [{ $gt: ['$discountPrice', 0] }, '$discountPrice', '$salePrice'],
                    },
                    totalValue: {
                        $multiply: [{ $ifNull: ['$quantity', 0] }, { $ifNull: ['$salePrice', 0] }],
                    },
                    storeName: { $ifNull: ['$_store.name', null] },
                    productName: { $ifNull: ['$_product.name', null] },
                    productSku: { $ifNull: ['$_product.sku', null] },
                    variantName: { $ifNull: ['$_variant.name', null] },
                    variantSku: { $ifNull: ['$_variant.sku', null] },
                },
            },

            {
                $project: {
                    _id: 0,
                    productType: 1,
                    storeId: 1,
                    storeName: 1,
                    productId: 1,
                    productName: 1,
                    productSku: 1,
                    productVariantId: 1,
                    variantName: 1,
                    variantSku: 1,
                    mrp: 1,
                    salePrice: 1,
                    discount: 1,
                    discountPrice: 1,
                    effectivePrice: 1,
                    quantity: 1,
                    totalValue: 1,
                    expiryDate: 1,
                    promotion: 1,
                    organizationId: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },

            { $sort: { storeName: 1, productName: 1, variantName: 1 } },
        ];

        return await this.inventoryModel.aggregate(pipeline)
            .allowDiskUse(true)
            .option({ maxTimeMS: 60_000, readPreference: 'secondaryPreferred' as any })
            .exec();
    }

    async bulkUploadInventory(rows: any[], user: any, storeId: string): Promise<UploadReport> {
        // ---- 0) Organization & store validation
        const organizationId = await this.getOrganizationId(user);
        if (!isValidId(organizationId)) {
            throw new BadRequestException('User has no valid organizationId');
        }
        if (!isValidId(storeId)) {
            throw new BadRequestException('Valid storeId is required in request body');
        }

        const orgOID = oid(organizationId);
        const storeOID = oid(storeId);

        const errors: RowIssue[] = [];
        const warnings: RowIssue[] = [];
        const invalidRows = new Set<number>(); // track row numbers that must be ignored

        // ---- helpers
        const toLower = (v: any) => String(v ?? '').trim().toLowerCase();
        const toStr = (v: any) => String(v ?? '').trim();
        const getCol = (r: any, names: string[]) => {
            for (const n of names) {
                if (Object.prototype.hasOwnProperty.call(r, n)) return r[n];
                const k = Object.keys(r).find((x) => x.toLowerCase() === n.toLowerCase());
                if (k) return r[k];
            }
            return undefined;
        };

        type NormRow = {
            __row: number;                         // 1-based CSV line (header is 1, so data starts at 2)
            productType: 'simple' | 'variable';
            productSku: string;
            variantSku: string | null;             // null for simple
            mrp: number;
            salePrice: number;
            quantity: number;
            expiryDate: Date | null;               // null if blank
        };

        // ---- 1) Per-row validation & normalization
        const norm: NormRow[] = [];

        rows.forEach((raw, idx) => {
            const rowNo = idx + 2; // header line = 1

            const productSku = toStr(getCol(raw, ['productSku', 'sku', 'master_sku']));
            const productType = toLower(getCol(raw, ['productType'])) as 'simple' | 'variable';
            const variantSkuRaw = toStr(getCol(raw, ['variantSku', 'productVariantSku']));

            const mrpRaw = getCol(raw, ['mrp']);
            const salePriceRaw = getCol(raw, ['salePrice']);
            const qtyRaw = getCol(raw, ['quantity']);
            const expiryRaw = toStr(getCol(raw, ['expiryDate']));

            // Required/shape checks
            if (!productSku) {
                errors.push({ row: rowNo, field: 'productSku', code: 'REQUIRED', message: 'productSku is required', sku: '' });
                invalidRows.add(rowNo);
            }
            if (!['simple', 'variable'].includes(productType)) {
                errors.push({ row: rowNo, field: 'productType', code: 'INVALID', message: 'productType must be "simple" or "variable"', sku: productSku });
                invalidRows.add(rowNo);
            }
            if (productType === 'variable' && !variantSkuRaw) {
                errors.push({ row: rowNo, field: 'variantSku', code: 'REQUIRED', message: 'variantSku is required for variable products',sku: productSku  });
                invalidRows.add(rowNo);
            }

            // Numbers
            const mrp = Number(mrpRaw);
            if (!Number.isFinite(mrp) || mrp < 0) {
                errors.push({ row: rowNo, field: 'mrp', code: 'INVALID', message: 'mrp must be a positive number',sku: productSku  });
                invalidRows.add(rowNo);
            }

            let salePrice: number;
            if (salePriceRaw === '' || salePriceRaw == null) {
                salePrice = Number.isFinite(mrp) ? mrp : NaN; // default rule
            } else {
                salePrice = Number(salePriceRaw);
                if (!Number.isFinite(salePrice) || salePrice <= 0) {
                    salePrice = mrp;
                    warnings.push({ row: rowNo, field: 'salePrice', code: 'DEFAULTED', message: 'salePrice invalid; set to mrp',sku: productSku  });
                }
            }
            // Enforce rule: salePrice must be <= mrp (adjust to < if you need strict)
            if (Number.isFinite(mrp) && Number.isFinite(salePrice) && !(salePrice <= mrp)) {
                errors.push({ row: rowNo, field: 'salePrice', code: 'PRICE_GTE_MRP', message: 'salePrice must be less than or equal to mrp',sku: productSku  });
                invalidRows.add(rowNo);
            }

            const quantity = Number(qtyRaw);
            if (!Number.isInteger(quantity) || quantity < 0) {
                errors.push({ row: rowNo, field: 'quantity', code: 'INVALID', message: 'quantity must be a non-negative integer',sku: productSku  });
                invalidRows.add(rowNo);
            }

            // expiryDate
            let expiryDate: Date | null = null;
            if (expiryRaw) {
                if (!/^\d{4}-\d{2}-\d{2}$/.test(expiryRaw)) {
                    errors.push({ row: rowNo, field: 'expiryDate', code: 'FORMAT', message: 'expiryDate must be YYYY-MM-DD',sku: productSku  });
                    invalidRows.add(rowNo);
                } else {
                    const d = new Date(expiryRaw);
                    if (Number.isNaN(+d)) {
                        errors.push({ row: rowNo, field: 'expiryDate', code: 'INVALID', message: 'expiryDate is not a valid date',sku: productSku  });
                        invalidRows.add(rowNo);
                    } else {
                        expiryDate = d;
                    }
                }
            }

            // variantSku given for simple product → ignore with warning
            if (productType === 'simple' && variantSkuRaw) {
                warnings.push({ row: rowNo, field: 'variantSku', code: 'IGNORED', message: 'variantSku provided for simple product; ignoring',sku: productSku  });
            }

            norm.push({
                __row: rowNo,
                productType,
                productSku,
                variantSku: productType === 'variable' ? (variantSkuRaw || null) : null,
                mrp: Math.round(mrp * 100) / 100,
                salePrice: Math.round(salePrice * 100) / 100,
                quantity,
                expiryDate,
            });
        });

        // ---- 2) Coalesce duplicates inside the file (last row wins)
        const keyOf = (r: NormRow) => `${organizationId}|${storeId}|${r.productSku}|${r.variantSku ?? ''}`;
        const coalesced = new Map<string, (NormRow & { __rows: number[] })>();

        for (const r of norm) {
            const k = keyOf(r);
            if (!coalesced.has(k)) {
                coalesced.set(k, { ...r, __rows: [r.__row] });
            } else {
                const prev = coalesced.get(k)!;
                prev.__rows.push(r.__row);
                // last row wins for quantity / price / expiry
                prev.quantity = r.quantity;
                prev.mrp = r.mrp;
                prev.salePrice = r.salePrice;
                prev.expiryDate = r.expiryDate;
                warnings.push({ row: r.__row, code: 'DUP_IN_FILE', message: 'Duplicate key in file: last row wins for quantity/price/expiry' });
            }
        }
        const items = Array.from(coalesced.values());

        // ---- 3) Resolve SKUs in batch (org-scoped)
        const skuSet = new Set(items.map((r) => r.productSku));
        const variantSkuSet = new Set(items.filter((r) => r.variantSku).map((r) => r.variantSku as string));

        const products = await this.productModel.find(
            { organizationId: orgOID, sku: { $in: Array.from(skuSet) } },
            { _id: 1, sku: 1 },
        ).lean();

        const variants = variantSkuSet.size
            ? await this.productVariantModel.find(
                { organizationId: orgOID, sku: { $in: Array.from(variantSkuSet) } },
                { _id: 1, sku: 1, productId: 1 },
            ).lean()
            : [];

        const skuToProductId = new Map<string, Types.ObjectId>();
        products.forEach((p: any) => skuToProductId.set(p.sku, p._id));

        const variantSkuTo = new Map<string, { variantId: Types.ObjectId; productId: Types.ObjectId }>();
        variants.forEach((v: any) => variantSkuTo.set(v.sku, { variantId: v._id, productId: v.productId }));

        // ---- 4) Referential checks (mark invalid rows if lookups fail)
        for (const r of items) {
            const baseRow = r.__rows[0]; // earliest CSV row for this key
            const pId = skuToProductId.get(r.productSku);
            if (!pId) {
                errors.push({ row: baseRow, field: 'productSku', code: 'UNKNOWN_SKU', message: `Unknown productSku "${r.productSku}"` });
                invalidRows.add(baseRow);
                continue;
            }
            if (r.productType === 'variable') {
                const ref = r.variantSku ? variantSkuTo.get(r.variantSku) : undefined;
                if (!ref) {
                    errors.push({ row: baseRow, field: 'variantSku', code: 'UNKNOWN_VARIANT', message: `Unknown variantSku "${r.variantSku}"` });
                    invalidRows.add(baseRow);
                    continue;
                }
                if (String(ref.productId) !== String(pId)) {
                    errors.push({ row: baseRow, field: 'variantSku', code: 'VARIANT_MISMATCH', message: `variantSku "${r.variantSku}" does not belong to productSku "${r.productSku}"` });
                    invalidRows.add(baseRow);
                }
            }
        }

        // ---- 5) Keep ONLY valid items for DB ops
        const validItems = items.filter((r) => !invalidRows.has(r.__rows[0]));

        if (!validItems.length) {
            return {
                summary: {
                    totalRows: rows.length,
                    validRows: 0,
                    invalidRows: invalidRows.size,
                    inserted: 0,
                    updated: 0,
                },
                errors,
                warnings,
            };
        }

        // ---- 6) Build bulkWrite ops from valid items only
        const ops: any[] = [];

        for (const r of validItems) {
            const productId = skuToProductId.get(r.productSku)!;
            const variantRef = r.variantSku ? variantSkuTo.get(r.variantSku) : undefined;
            const productVariantId = variantRef?.variantId ?? null;

            const filter: any = {
                organizationId: orgOID,
                storeId: storeOID,
                productId,
                productType: r.productType,
                productVariantId: r.productType === 'variable' ? productVariantId : null,
            };

            const setDoc: any = {
                organizationId: orgOID,
                storeId: storeOID,
                productId,
                productType: r.productType,
                mrp: r.mrp,
                salePrice: r.salePrice,
                discount: 0,
                discountPrice: 0,
                quantity: r.quantity, // SET quantity (not increment)
                updatedAt: new Date(),
            };
            if (r.productType === 'variable') setDoc.productVariantId = productVariantId;
            if (r.expiryDate) setDoc.expiryDate = r.expiryDate; // only overwrite if provided

            ops.push({
                updateOne: {
                    filter,
                    update: { $set: setDoc },
                    upsert: true, // update existing or create new
                },
            });
        }

        // ---- 7) Execute bulkWrite
        const result = await this.inventoryModel.bulkWrite(ops, { ordered: false });
        const inserted = (result as any).upsertedCount || 0;
        const updated = (result as any).modifiedCount || 0;

        return {
            summary: {
                totalRows: rows.length,
                validRows: validItems.length,
                invalidRows: invalidRows.size,
                inserted,
                updated,
            },
            errors,
            warnings,
        };
    }

    // async bulkUploadInventory(rows: any[], user: any, storeId: string): Promise<UploadReport> {
    //     let organizationId = await this.getOrganizationId(user)

    //     if (!isValidId(organizationId)) {
    //         throw new BadRequestException('User has no valid organizationId');
    //     }
    //     if (!isValidId(storeId)) {
    //         throw new BadRequestException('Valid storeId is required in request body');
    //     }
    //     const orgOID = oid(organizationId);
    //     const storeOID = oid(storeId);

    //     const errors: RowIssue[] = [];
    //     const warnings: RowIssue[] = [];

    //     // ---- helpers
    //     const toLower = (v: any) => String(v ?? '').trim().toLowerCase();
    //     const toStr = (v: any) => String(v ?? '').trim();
    //     const getCol = (r: any, names: string[]) => {
    //         for (const n of names) {
    //             if (Object.prototype.hasOwnProperty.call(r, n)) return r[n];
    //             const k = Object.keys(r).find((x) => x.toLowerCase() === n.toLowerCase());
    //             if (k) return r[k];
    //         }
    //         return undefined;
    //     };

    //     type NormRow = {
    //         __row: number;                        // first CSV row number this normalized row came from
    //         productType: 'simple' | 'variable';
    //         productSku: string;
    //         variantSku: string | null;
    //         mrp: number;
    //         salePrice: number;
    //         quantity: number;
    //         expiryDate: Date | null;              // null if blank
    //     };

    //     // ---- 1) Per-row validation & normalization
    //     const norm: NormRow[] = [];

    //     rows.forEach((raw, idx) => {
    //         const rowNo = idx + 2; // header line = 1

    //         const productSku = toStr(getCol(raw, ['productSku', 'sku', 'master_sku']));
    //         const productType = toLower(getCol(raw, ['productType'])) as 'simple' | 'variable';
    //         const variantSkuRaw = toStr(getCol(raw, ['variantSku', 'productVariantSku']));

    //         const mrpRaw = getCol(raw, ['mrp']);
    //         let salePriceRaw = getCol(raw, ['salePrice']);
    //         const qtyRaw = getCol(raw, ['quantity']);
    //         const expiryRaw = toStr(getCol(raw, ['expiryDate']));

    //         // Required fields
    //         if (!productSku) errors.push({ row: rowNo, field: 'productSku', code: 'REQUIRED', message: 'productSku is required' });
    //         if (!['simple', 'variable'].includes(productType)) {
    //             errors.push({ row: rowNo, field: 'productType', code: 'INVALID', message: 'productType must be "simple" or "variable"' });
    //         }
    //         if (productType === 'variable' && !variantSkuRaw) {
    //             errors.push({ row: rowNo, field: 'variantSku', code: 'REQUIRED', message: 'variantSku is required for variable products' });
    //         }

    //         // Numbers
    //         const mrp = Number(mrpRaw);
    //         if (!Number.isFinite(mrp) || mrp < 0) {
    //             errors.push({ row: rowNo, field: 'mrp', code: 'INVALID', message: 'mrp must be a positive number' });
    //         }

    //         let salePrice: number;
    //         if (salePriceRaw === '' || salePriceRaw == null) {
    //             salePrice = Number.isFinite(mrp) ? mrp : NaN; // default rule
    //         } else {
    //             salePrice = Number(salePriceRaw);
    //             if (!Number.isFinite(salePrice) || salePrice <= 0) {
    //                 salePrice = mrp;
    //                 warnings.push({ row: rowNo, field: 'salePrice', code: 'DEFAULTED', message: 'salePrice invalid; set to mrp' });
    //             }
    //         }
    //         // Enforce rule: salePrice must be < mrp
    //         if (Number.isFinite(mrp) && Number.isFinite(salePrice) && !(salePrice <= mrp)) {
    //             errors.push({ row: rowNo, field: 'salePrice', code: 'PRICE_GTE_MRP', message: 'salePrice must be less than mrp' });
    //         }

    //         const quantity = Number(qtyRaw);
    //         console.log(quantity)
    //         if (!Number.isInteger(quantity) || quantity < 0) {
    //             errors.push({ row: rowNo, field: 'quantity', code: 'INVALID', message: 'quantity must be a non-negative integer' });
    //          invalidRows.add(rowNo);    
    //         }

    //         // expiryDate
    //         let expiryDate: Date | null = null;
    //         if (expiryRaw) {
    //             if (!/^\d{4}-\d{2}-\d{2}$/.test(expiryRaw)) {
    //                 errors.push({ row: rowNo, field: 'expiryDate', code: 'FORMAT', message: 'expiryDate must be YYYY-MM-DD' });
    //             } else {
    //                 const d = new Date(expiryRaw);
    //                 if (Number.isNaN(+d)) {
    //                     errors.push({ row: rowNo, field: 'expiryDate', code: 'INVALID', message: 'expiryDate is not a valid date' });
    //                 } else {
    //                     expiryDate = d;
    //                 }
    //             }
    //         }

    //         // variantSku given for simple product → ignore with warning
    //         if (productType === 'simple' && variantSkuRaw) {
    //             warnings.push({ row: rowNo, field: 'variantSku', code: 'IGNORED', message: 'variantSku provided for simple product; ignoring' });
    //         }

    //         norm.push({
    //             __row: rowNo,
    //             productType,
    //             productSku,
    //             variantSku: productType === 'variable' ? (variantSkuRaw || null) : null,
    //             mrp: Math.round(mrp * 100) / 100,
    //             salePrice: Math.round(salePrice * 100) / 100,
    //             quantity,
    //             expiryDate,
    //         });
    //     });

    //     // if (errors.length) {
    //     //     return {
    //     //         summary: { totalRows: rows.length, validRows: Math.max(0, rows.length - errors.length), invalidRows: errors.length, inserted: 0, updated: 0 },
    //     //         errors, warnings,
    //     //     };
    //     // }

    //     // ---- 2) Coalesce duplicates inside the file (last row wins)
    //     const keyOf = (r: NormRow) => `${organizationId}|${storeId}|${r.productSku}|${r.variantSku ?? ''}`;
    //     const coalesced = new Map<string, NormRow & { __rows: number[] }>();

    //     for (const r of norm) {
    //         const k = keyOf(r);
    //         if (!coalesced.has(k)) {
    //             coalesced.set(k, { ...r, __rows: [r.__row] });
    //         } else {
    //             const prev = coalesced.get(k)!;
    //             prev.__rows.push(r.__row);
    //             // last row wins for quantity / price / expiry
    //             prev.quantity = r.quantity;
    //             prev.mrp = r.mrp;
    //             prev.salePrice = r.salePrice;
    //             prev.expiryDate = r.expiryDate;
    //             warnings.push({ row: r.__row, code: 'DUP_IN_FILE', message: 'Duplicate key in file: last row wins for quantity/price/expiry' });
    //         }
    //     }
    //     const items = Array.from(coalesced.values());

    //     // ---- 3) Resolve SKUs in batch (org-scoped)
    //     const skuSet = new Set(items.map((r) => r.productSku));
    //     const variantSkuSet = new Set(items.filter((r) => r.variantSku).map((r) => r.variantSku as string));

    //     const products = await this.productModel.find(
    //         { organizationId: orgOID, sku: { $in: Array.from(skuSet) } },
    //         { _id: 1, sku: 1 },
    //     ).lean();

    //     const variants = variantSkuSet.size
    //         ? await this.productVariantModel.find(
    //             { organizationId: orgOID, sku: { $in: Array.from(variantSkuSet) } },
    //             { _id: 1, sku: 1, productId: 1 },
    //         ).lean()
    //         : [];

    //     const skuToProductId = new Map<string, Types.ObjectId>();
    //     products.forEach((p: any) => skuToProductId.set(p.sku, p._id));

    //     const variantSkuTo = new Map<string, { variantId: Types.ObjectId; productId: Types.ObjectId }>();
    //     variants.forEach((v: any) => variantSkuTo.set(v.sku, { variantId: v._id, productId: v.productId }));

    //     // Check referential validity
    //     for (const r of items) {
    //         const pId = skuToProductId.get(r.productSku);
    //         if (!pId) {
    //             errors.push({ row: r.__rows[0], field: 'productSku', code: 'UNKNOWN_SKU', message: `Unknown productSku "${r.productSku}"` });
    //             continue;
    //         }
    //         if (r.productType === 'variable') {
    //             const ref = r.variantSku ? variantSkuTo.get(r.variantSku) : undefined;
    //             if (!ref) {
    //                 errors.push({ row: r.__rows[0], field: 'variantSku', code: 'UNKNOWN_VARIANT', message: `Unknown variantSku "${r.variantSku}"` });
    //                 continue;
    //             }
    //             if (String(ref.productId) !== String(pId)) {
    //                 errors.push({ row: r.__rows[0], field: 'variantSku', code: 'VARIANT_MISMATCH', message: `variantSku "${r.variantSku}" does not belong to productSku "${r.productSku}"` });
    //             }
    //         }
    //     }

    //     // if (errors.length) {
    //     //     return {
    //     //         summary: { totalRows: rows.length, validRows: Math.max(0, items.length - errors.length), invalidRows: errors.length, inserted: 0, updated: 0 },
    //     //         errors, warnings,
    //     //     };
    //     // }

    //     // ---- 4) Build bulkWrite ops (SET quantity/mrp/salePrice; SET expiry when provided)
    //     const ops: any[] = [];

    //     for (const r of items) {
    //         const productId = skuToProductId.get(r.productSku)!;
    //         const variantRef = r.variantSku ? variantSkuTo.get(r.variantSku) : undefined;
    //         const productVariantId = variantRef?.variantId ?? null;

    //         const filter: any = {
    //             organizationId: orgOID,
    //             storeId: storeOID,
    //             productId,
    //             productType: r.productType,
    //             productVariantId: r.productType === 'variable' ? productVariantId : null,
    //         };

    //         const setDoc: any = {
    //             organizationId: orgOID,
    //             storeId: storeOID,
    //             productId,
    //             productType: r.productType,
    //             mrp: r.mrp,
    //             salePrice: r.salePrice,
    //             discount: 0,
    //             discountPrice: 0,
    //             quantity: r.quantity,             // SET quantity (not increment)
    //             updatedAt: new Date(),
    //         };
    //         if (r.productType === 'variable') setDoc.productVariantId = productVariantId;
    //         if (r.expiryDate) setDoc.expiryDate = r.expiryDate; // only overwrite if provided

    //         ops.push({
    //             updateOne: {
    //                 filter,
    //                 update: { $set: setDoc },
    //                 upsert: true, // update existing or create new
    //             },
    //         });
    //     }

    //     // ---- 5) Execute bulkWrite
    //     const result = await this.inventoryModel.bulkWrite(ops, { ordered: false });
    //     const inserted = result.upsertedCount || 0;
    //     const updated = result.modifiedCount || 0;

    //     return {
    //         summary: {
    //             totalRows: rows.length,
    //             validRows: items.length,
    //             invalidRows: 0,
    //             inserted,
    //             updated,
    //         },
    //         errors,
    //         warnings,
    //     };
    // }
    /****************************************** */
    //      async bulkUploadInventory(
    //     rows: any[],
    //     user: any,
    //     storeId: string,
    //     isUpdate: boolean, // <-- NEW flag controls behavior
    //   ): Promise<UploadReport> {
    //     const organizationId = await this.getOrganizationId(user);
    //     if (!isValidId(organizationId)) throw new BadRequestException('User has no valid organizationId');
    //     if (!isValidId(storeId)) throw new BadRequestException('Valid storeId is required in request body');

    //     const orgOID = oid(organizationId);
    //     const storeOID = oid(storeId);

    //     const errors: RowIssue[] = [];
    //     const warnings: RowIssue[] = [];

    //     // ---- helpers
    //     const toLower = (v: any) => String(v ?? '').trim().toLowerCase();
    //     const toStr = (v: any) => String(v ?? '').trim();
    //     const getCol = (r: any, names: string[]) => {
    //       for (const n of names) {
    //         if (Object.prototype.hasOwnProperty.call(r, n)) return r[n];
    //         const k = Object.keys(r).find((x) => x.toLowerCase() === n.toLowerCase());
    //         if (k) return r[k];
    //       }
    //       return undefined;
    //     };

    //     type NormRow = {
    //       __row: number;
    //       productType: 'simple' | 'variable';
    //       productSku: string;
    //       variantSku: string | null;
    //       mrp: number;
    //       salePrice: number;
    //       quantity: number;
    //       expiryDate: Date | null;
    //     };

    //     // ---- 1) Validate + normalize rows
    //     const norm: NormRow[] = [];
    //     rows.forEach((raw, idx) => {
    //       const rowNo = idx + 2; // CSV header = 1

    //       const productSku = toStr(getCol(raw, ['productSku', 'sku', 'master_sku']));
    //       const productType = toLower(getCol(raw, ['productType'])) as 'simple' | 'variable';
    //       const variantSkuRaw = toStr(getCol(raw, ['variantSku', 'productVariantSku']));
    //       const mrpRaw = getCol(raw, ['mrp']);
    //       let salePriceRaw = getCol(raw, ['salePrice']);
    //       const qtyRaw = getCol(raw, ['quantity']);
    //       const expiryRaw = toStr(getCol(raw, ['expiryDate']));

    //       if (!productSku) errors.push({ row: rowNo, field: 'productSku', code: 'REQUIRED', message: 'productSku is required' });
    //       if (!['simple', 'variable'].includes(productType)) {
    //         errors.push({ row: rowNo, field: 'productType', code: 'INVALID', message: 'productType must be "simple" or "variable"' });
    //       }
    //       if (productType === 'variable' && !variantSkuRaw) {
    //         errors.push({ row: rowNo, field: 'variantSku', code: 'REQUIRED', message: 'variantSku is required for variable products' });
    //       }

    //       const mrp = Number(mrpRaw);
    //       if (!Number.isFinite(mrp) || mrp <= 0) {
    //         errors.push({ row: rowNo, field: 'mrp', code: 'INVALID', message: 'mrp must be a positive number' });
    //       }

    //       let salePrice: number;
    //       if (salePriceRaw === '' || salePriceRaw == null) {
    //         salePrice = Number.isFinite(mrp) ? mrp : NaN;
    //       } else {
    //         salePrice = Number(salePriceRaw);
    //         if (!Number.isFinite(salePrice) || salePrice <= 0) {
    //           salePrice = mrp;
    //           warnings.push({ row: rowNo, field: 'salePrice', code: 'DEFAULTED', message: 'salePrice invalid; set to mrp' });
    //         }
    //       }
    //       if (Number.isFinite(mrp) && Number.isFinite(salePrice) && !(salePrice < mrp)) {
    //         errors.push({ row: rowNo, field: 'salePrice', code: 'PRICE_GTE_MRP', message: 'salePrice must be less than mrp' });
    //       }

    //       const quantity = Number(qtyRaw);
    //       if (!Number.isInteger(quantity) || quantity < 0) {
    //         errors.push({ row: rowNo, field: 'quantity', code: 'INVALID', message: 'quantity must be a non-negative integer' });
    //       }

    //       let expiryDate: Date | null = null;
    //       if (expiryRaw) {
    //         if (!/^\d{4}-\d{2}-\d{2}$/.test(expiryRaw)) {
    //           errors.push({ row: rowNo, field: 'expiryDate', code: 'FORMAT', message: 'expiryDate must be YYYY-MM-DD' });
    //         } else {
    //           const d = new Date(expiryRaw);
    //           if (Number.isNaN(+d)) errors.push({ row: rowNo, field: 'expiryDate', code: 'INVALID', message: 'expiryDate is not a valid date' });
    //           else expiryDate = d;
    //         }
    //       }

    //       if (productType === 'simple' && variantSkuRaw) {
    //         warnings.push({ row: rowNo, field: 'variantSku', code: 'IGNORED', message: 'variantSku provided for simple product; ignoring' });
    //       }

    //       norm.push({
    //         __row: rowNo,
    //         productType,
    //         productSku,
    //         variantSku: productType === 'variable' ? (variantSkuRaw || null) : null,
    //         mrp: Math.round(mrp * 100) / 100,
    //         salePrice: Math.round(salePrice * 100) / 100,
    //         quantity,
    //         expiryDate,
    //       });
    //     });

    //     if (errors.length) {
    //       return {
    //         summary: { totalRows: rows.length, validRows: Math.max(0, rows.length - errors.length), invalidRows: errors.length, inserted: 0, updated: 0, skipped: 0 },
    //         errors, warnings,
    //       };
    //     }

    //     // ---- 2) Coalesce duplicates (last row wins)
    //     const keyOf = (r: NormRow) => `${organizationId}|${storeId}|${r.productSku}|${r.variantSku ?? ''}`;
    //     const coalesced = new Map<string, NormRow & { __rows: number[] }>();
    //     for (const r of norm) {
    //       const k = keyOf(r);
    //       if (!coalesced.has(k)) {
    //         coalesced.set(k, { ...r, __rows: [r.__row] });
    //       } else {
    //         const prev = coalesced.get(k)!;
    //         prev.__rows.push(r.__row);
    //         prev.quantity = r.quantity;
    //         prev.mrp = r.mrp;
    //         prev.salePrice = r.salePrice;
    //         prev.expiryDate = r.expiryDate;
    //         warnings.push({ row: r.__row, code: 'DUP_IN_FILE', message: 'Duplicate key in file: last row wins for quantity/price/expiry' });
    //       }
    //     }
    //     const items = Array.from(coalesced.values()); // <-- THIS is "items"

    //     // ---- 3) Resolve SKUs in batch (org-scoped)
    //     const skuSet = new Set(items.map((r) => r.productSku));
    //     const variantSkuSet = new Set(items.filter((r) => r.variantSku).map((r) => r.variantSku as string));

    //     const products = await this.productModel.find(
    //       { organizationId: orgOID, sku: { $in: Array.from(skuSet) } },
    //       { _id: 1, sku: 1 },
    //     ).lean();

    //     const variants = variantSkuSet.size
    //       ? await this.productVariantModel.find(
    //           { organizationId: orgOID, sku: { $in: Array.from(variantSkuSet) } },
    //           { _id: 1, sku: 1, productId: 1 },
    //         ).lean()
    //       : [];

    //     const skuToProductId = new Map<string, Types.ObjectId>();
    //     products.forEach((p: any) => skuToProductId.set(p.sku, p._id));

    //     const variantSkuTo = new Map<string, { variantId: Types.ObjectId; productId: Types.ObjectId }>();
    //     variants.forEach((v: any) => variantSkuTo.set(v.sku, { variantId: v._id, productId: v.productId }));

    //     for (const r of items) {
    //       const pId = skuToProductId.get(r.productSku);
    //       if (!pId) {
    //         errors.push({ row: r.__rows[0], field: 'productSku', code: 'UNKNOWN_SKU', message: `Unknown productSku "${r.productSku}"` });
    //         continue;
    //       }
    //       if (r.productType === 'variable') {
    //         const ref = r.variantSku ? variantSkuTo.get(r.variantSku) : undefined;
    //         if (!ref) {
    //           errors.push({ row: r.__rows[0], field: 'variantSku', code: 'UNKNOWN_VARIANT', message: `Unknown variantSku "${r.variantSku}"` });
    //           continue;
    //         }
    //         if (String(ref.productId) !== String(pId)) {
    //           errors.push({ row: r.__rows[0], field: 'variantSku', code: 'VARIANT_MISMATCH', message: `variantSku "${r.variantSku}" does not belong to productSku "${r.productSku}"` });
    //         }
    //       }
    //     }

    //     if (errors.length) {
    //       return {
    //         summary: { totalRows: rows.length, validRows: Math.max(0, items.length - errors.length), invalidRows: errors.length, inserted: 0, updated: 0, skipped: 0 },
    //         errors, warnings,
    //       };
    //     }

    //     // ---- 3.5) Prefetch existing inventory (ONE query) to know existence
    //     const keyOfFilter = (
    //       productId: Types.ObjectId,
    //       productType: 'simple' | 'variable',
    //       productVariantId: Types.ObjectId | null,
    //     ) => `${organizationId}|${storeId}|${productId.toString()}|${productType}|${productVariantId ? productVariantId.toString() : ''}`;

    //     const probeOr: any[] = [];
    //     const meta: Array<{
    //       key: string;
    //       rowNo: number;
    //       productId: Types.ObjectId;
    //       productType: 'simple' | 'variable';
    //       productVariantId: Types.ObjectId | null;
    //       r: (typeof items)[number];
    //     }> = [];

    //     for (const r of items) {
    //       const productId = skuToProductId.get(r.productSku)!;
    //       const variantRef = r.variantSku ? variantSkuTo.get(r.variantSku) : undefined;
    //       const productVariantId = r.productType === 'variable' ? (variantRef?.variantId ?? null) : null;

    //       const filter = {
    //         organizationId: orgOID,
    //         storeId: storeOID,
    //         productId,
    //         productType: r.productType,
    //         productVariantId: r.productType === 'variable' ? productVariantId : null,
    //       };
    //       const key = keyOfFilter(productId, r.productType, filter.productVariantId);
    //       probeOr.push(filter);
    //       meta.push({ key, rowNo: r.__rows[0], productId, productType: r.productType, productVariantId: filter.productVariantId, r });
    //     }

    //     const existingDocs = probeOr.length
    //       ? await this.inventoryModel.find(
    //           { $or: probeOr },
    //           { _id: 1, productId: 1, productType: 1, productVariantId: 1 },
    //         ).lean()
    //       : [];

    //     const existsByKey = new Map<string, boolean>();
    //     for (const d of existingDocs) {
    //       const k = keyOfFilter(d.productId as any, d.productType, (d as any).productVariantId ?? null);
    //       existsByKey.set(k, true);
    //     }

    //     // ---- 4) Build ops per isUpdate + collect warnings for skipped
    //     const ops: any[] = [];
    //     let skipped = 0;

    //     for (const it of meta) {
    //       const { key, rowNo, productId, productType, productVariantId, r } = it;
    //       const exists = !!existsByKey.get(key);

    //       const setDoc: any = {
    //         organizationId: orgOID,
    //         storeId: storeOID,
    //         productId,
    //         productType,
    //         mrp: r.mrp,
    //         salePrice: r.salePrice,
    //         discount: 0,
    //         discountPrice: 0,
    //         quantity: r.quantity, // replace quantity
    //         updatedAt: new Date(),
    //       };
    //       if (productType === 'variable') setDoc.productVariantId = productVariantId;
    //       if (r.expiryDate) setDoc.expiryDate = r.expiryDate;

    //       const filter: any = {
    //         organizationId: orgOID,
    //         storeId: storeOID,
    //         productId,
    //         productType,
    //         productVariantId: productType === 'variable' ? productVariantId : null,
    //       };

    //       if (isUpdate) {
    //         // only update existing; skip if not found
    //         if (!exists) {
    //           warnings.push({
    //             row: rowNo,
    //             code: 'NOT_FOUND_UPDATE_SKIPPED',
    //             message: `Inventory row not found for productSku="${r.productSku}"${r.variantSku ? ` variantSku="${r.variantSku}"` : ''}; update skipped.`,
    //           });
    //           skipped++;
    //           continue;
    //         }
    //         ops.push({
    //           updateOne: {
    //             filter,
    //             update: { $set: setDoc },
    //             upsert: false,
    //           },
    //         });
    //       } else {
    //         // only insert new; skip if exists
    //         if (exists) {
    //           warnings.push({
    //             row: rowNo,
    //             code: 'EXISTS_INSERT_SKIPPED',
    //             message: `Inventory row already exists for productSku="${r.productSku}"${r.variantSku ? ` variantSku="${r.variantSku}"` : ''}; insert skipped.`,
    //           });
    //           skipped++;
    //           continue;
    //         }
    //         ops.push({
    //           updateOne: {
    //             filter,
    //             update: { $setOnInsert: setDoc },
    //             upsert: true,
    //           },
    //         });
    //       }
    //     }

    //     // ---- 5) Execute bulkWrite
    //     const result = ops.length ? await this.inventoryModel.bulkWrite(ops, { ordered: false }) : { upsertedCount: 0, modifiedCount: 0 };
    //     const inserted = (result as any).upsertedCount || 0;
    //     const updated = (result as any).modifiedCount || 0;

    //     return {
    //       summary: {
    //         totalRows: rows.length,
    //         validRows: items.length,
    //         invalidRows: 0,
    //         inserted,
    //         updated,
    //         skipped,
    //       },
    //       errors,
    //       warnings,
    //     };
    //   }
    async getProductsAsInventoryTemplate(user: any): Promise<TemplateRow[]> {
        const organizationId = await this.getOrganizationId(user);
        if (!organizationId || !Types.ObjectId.isValid(organizationId)) {
            throw new BadRequestException('User has no valid organizationId');
        }

        const orgOID = new Types.ObjectId(organizationId);

        // 1) Fetch products + (for variable) variants in one go
        const products = await this.productModel.aggregate([
            { $match: { organizationId: orgOID } }, // add {deletedAt: {$exists: false}} if you soft-delete
            {
                $lookup: {
                    from: 'productvariants',
                    localField: '_id',
                    foreignField: 'productId',
                    as: 'variants',
                    pipeline: [
                        // { $match: { deletedAt: { $exists: false } } },
                        { $project: { _id: 1, sku: 1, name: 1, mrp: 1, salePrice: 1, productId: 1 } },
                    ],
                },
            },
            {
                $project: {
                    _id: 1,
                    name: 1,
                    sku: 1,
                    type: 1,     // 'simple' | 'variable'
                    mrp: 1,
                    salePrice: 1,
                    variants: 1,
                },
            },
        ]).exec();

        // 2) If storeId present, index inventory by (productId|variantId)
        let invIndex = new Map<
            string,
            { mrp?: number; salePrice?: number; quantity?: number; expiryDate?: Date }
        >();



        // 3) Build output rows in the exact import shape
        const rows: TemplateRow[] = [];

        const fmtDate = (val?: Date | string | null) =>
            val ? new Date(val).toISOString().slice(0, 10) : '';
        for (const p of products) {
            if (p.type === 'simple') {
                rows.push({
                    productName: p.name ?? '',
                    variantTitle: '',
                    productSku: p.sku ?? '',
                    productType: 'simple',
                    variantSku: '',
                    mrp: '',
                    salePrice: '',
                    quantity: '',
                    expiryDate: '',
                });
            } else {
                // variable → one row per variant
                const variants = Array.isArray(p.variants) ? p.variants : [];
                for (const v of variants) {
                    rows.push({
                        productName: p.name ?? '',
                        variantTitle: v.name ?? '',
                        productSku: p.sku ?? '',
                        productType: 'variable',
                        variantSku: v.sku ?? '',
                        mrp: '',
                        salePrice: '',
                        quantity: '',
                        expiryDate: '',
                    });
                }
            }
        }

        // 4) Stable ordering: by productSku, then variantSku
        rows.sort(
            (a, b) =>
                (a.productSku || '').localeCompare(b.productSku || '') ||
                (a.variantSku || '').localeCompare(b.variantSku || '')
        );
        return rows;
    }


}


