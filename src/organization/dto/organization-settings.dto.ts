import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsObject, IsOptional } from "class-validator";

export class OrganizationSettingsDto {
    @ApiProperty({
        description: "Client onboarding details",
        example: "{}",
        required: true,
    })
    @IsObject({ message: "Required client onboarding options" })
    @IsOptional()
    clientOnboarding?: object;

    @ApiProperty({
        description: "Staff onboarding details.",
        example: "{}",
        required: true,
    })
    @IsObject({ message: "Required staff onboarding options" })
    @IsOptional()
    staffOnboarding?: object;

    @ApiProperty({
        description: "Is the organization inclusive of GST for all pricing setting",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid value for GST inclusion" })
    @Type(() => Boolean)
    @IsOptional()
    isInclusiveGst?: boolean;
}
