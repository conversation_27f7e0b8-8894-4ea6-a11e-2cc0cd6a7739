import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nestjs/common";
import { Observable, tap } from "rxjs";

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const { method, url, body } = req;
    const start = Date.now();

    this.logger.log(`📥 Request: ${method} ${url} body=${JSON.stringify(body)}-----${Date.now()}`);

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - start;
        this.logger.log(
          `📤 Response: ${method} ${url} [${duration}ms] → ${JSON.stringify(data)}---${Date.now()}`,
        );
      }),
    );
  }
}