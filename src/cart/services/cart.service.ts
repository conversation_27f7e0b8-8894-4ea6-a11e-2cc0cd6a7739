import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Pricing } from 'src/organization/schemas/pricing.schema';
import { Inventory, InventoryDocument } from 'src/merchandise/schema/inventory.schema';
import { CustomPackage } from 'src/customPackage/schemas/custom-package.schema';
import { CartRequestDto } from '../dto/cart.request.dto';
import { CartItemDetailDto, CartResponseDto } from '../dto/response/cart.response.dto';
import { ENUM_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { PromotionService } from 'src/promotions/services/promotion.service';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { Clients } from 'src/users/schemas/clients.schema';
import { PromotionDocument } from 'src/promotions/repository/entities/promotion.entity';
import { ENUM_PROMOTION_TARGET } from 'src/promotions/enums/promotion-target.enum';
import { PromotionItemService } from 'src/promotions/services/promotion-items.service';
import { ActiveTimeFrameService } from 'src/utils/services/active-time-frame.service';
import { ProductVariant, ProductVariantDocument } from 'src/merchandise/schema/product-variant.schema';
import { OrganizationSettingService } from 'src/organizationSettings/services/organization-settings.service';
import { PolicyService } from 'src/policy/services/policy.service';
import { ENUM_POLICY_ACTION, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { Purchase } from 'src/users/schemas/purchased-packages.schema';
import { Invoice } from 'src/users/schemas/invoice.schema';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { ProductDocument } from 'src/merchandise/schema/product.schema';

@Injectable()
export class CartService {
  constructor(
    @InjectModel(Pricing.name) private pricingModel: Model<Pricing>,
    @InjectModel(Inventory.name) private inventoryModel: Model<Inventory>,
    @InjectModel(CustomPackage.name) private customPackageModel: Model<CustomPackage>,
    @InjectModel(Clients.name) private clientModel: Model<Clients>,
    @InjectModel(Purchase.name) private purchaseModel: Model<Purchase>,
    @InjectModel(Invoice.name) private invoiceModel: Model<Invoice>,
    private readonly policyService: PolicyService,
    private readonly organizationSettingService: OrganizationSettingService,
    private readonly promotionService: PromotionService,
    private readonly promotionItemService: PromotionItemService,
    private readonly activeTimeFrameService: ActiveTimeFrameService,
  ) { }

  /**
   * Process a cart request and return detailed pricing information
   */
  async processCart(cartRequest: CartRequestDto, autoApplyPromotion: boolean = false): Promise<CartResponseDto> {
    const { organizationId, userId, facilityId, items, discount, returnItems, returnCustomItems } = cartRequest;
    // const isPinEnabled = await this.organizationSettingService.isEnabled(new Types.ObjectId(organizationId), 'settings_pin')
    const isCartDiscountEnabled = await this.organizationSettingService.isEnabled(new Types.ObjectId(organizationId), 'subsettings_discounts_custom_discount')

    // Check if the cart is empty
    if (!items || items.length === 0) {
      throw new BadRequestException('Cart is empty');
    }

    // Process each item in the cart
    const processedItems: CartItemDetailDto[] = [];
    let subtotal = 0;
    let itemLevelDiscount = 0;
    let totalTax = 0;
    let isValid = true;
    const validationErrors: string[] = [];

    // Get user details to check if they are a member (for member-only promotions)
    const client = userId ? await this.clientModel.findOne({ userId: new Types.ObjectId(userId) }) : null;
    const isMember = client && client.membershipId ? true : false;

    // Process return purchases if provided
    let totalEffectiveReturnUnitPrice = 0;
    let returnDetails = null;
    let returnItemsDetails = [];
    if (returnItems && returnItems.length > 0 && userId) {
      try {
        const returnPurchases = await this.processReturnPurchases(returnItems, organizationId, userId);
        totalEffectiveReturnUnitPrice += returnPurchases.totalReturnValue;
        returnItemsDetails = returnPurchases.returnItemsDetails;
        returnDetails = {
          returnPurchaseIds: returnItems,
          returnTotal: returnPurchases.totalReturnValue,
        };
      } catch (error) {
        validationErrors.push(`Return purchases error: ${error.message}`);
        isValid = false;
      }
    }

    // Process return purchases if provided
    let returnCustomPackageDetails = null;
    let returnCustomPackageItemDetails = [];
    if (returnCustomItems && returnCustomItems.length > 0 && userId) {
      try {
        const returnPurchases = await this.processReturnCustomItemPurchases(returnCustomItems, organizationId, userId);
        totalEffectiveReturnUnitPrice += returnPurchases.totalReturnValue;
        returnCustomPackageItemDetails = returnPurchases.returnItemsDetails;
        returnCustomPackageDetails = {
          returnPurchaseIds: returnPurchases.returnItemsDetails.map(item => item.purchaseId),
          returnTotal: returnPurchases.totalReturnValue,
        };
      } catch (error) {
        validationErrors.push(`Return custom item purchases error: ${error.message}`);
        isValid = false;
      }
    }

    // First pass: Process each item with item-level discounts only
    for (const item of items) {
      try {
        const itemDetail = await this.processCartItem(
          item,
          organizationId,
          isMember,
          facilityId,
          autoApplyPromotion
        );
        // Add item to processed items regardless of validity
        itemDetail.discountedBy = item?.discount?.discountedBy ?? null
        processedItems.push(itemDetail);

        // Always add to totals, even for invalid items
        subtotal += itemDetail.totalPrice;
        itemLevelDiscount += itemDetail.discountAmount;

        // Update cart validity if any item is invalid
        if (!itemDetail.isValid) {
          isValid = false;
          // Add item-specific error to cart-level validation errors
          if (itemDetail.validationErrors?.length) {
            validationErrors.push(`${itemDetail.name || 'Item'}: ${itemDetail.validationErrors.join(', ')}`);
          }
        }
      } catch (error) {
        // Skip items that are not found
        if (error instanceof NotFoundException) {
          continue;
        }
        isValid = false;
        validationErrors.push(`${error.message}`);
      }
    }

    // Apply return discount to items BEFORE cart-level discounts
    let returnLevelDiscount = 0;
    if (totalEffectiveReturnUnitPrice > 0 && processedItems.length > 0) {
      // Calculate total amount after item-level discounts for proportional distribution
      const totalAmountAfterItemDiscount = processedItems.reduce((sum, item) => {
        const itemTotal = item.price * item.quantity;
        const itemDiscount = item.discountAmount || 0;
        return sum + (itemTotal - itemDiscount);
      }, 0);

      let allocatedReturnDiscount = 0;

      for (let i = 0; i < processedItems.length; i++) {
        const item = processedItems[i];
        const itemTotal = item.price * item.quantity;
        const itemDiscount = item.discountAmount || 0;
        const amountAfterItemDiscount = itemTotal - itemDiscount;

        if (amountAfterItemDiscount > 0) {
          let returnDiscountAmount = 0;

          // Handle last item to ensure exact total
          if (i === processedItems.length - 1) {
            returnDiscountAmount = Math.min(
              totalEffectiveReturnUnitPrice - allocatedReturnDiscount,
              amountAfterItemDiscount
            );
          } else {
            const ratio = amountAfterItemDiscount / totalAmountAfterItemDiscount;
            returnDiscountAmount = Number((ratio * totalEffectiveReturnUnitPrice));
            returnDiscountAmount = Math.min(returnDiscountAmount, amountAfterItemDiscount);
          }

          allocatedReturnDiscount += returnDiscountAmount;
          returnLevelDiscount += returnDiscountAmount;

          // Apply return discount to item
          item.returnDiscountAmount = returnDiscountAmount;

          // Update item pricing with return discount applied
          const priceAfterItemDiscount = itemTotal - itemDiscount;
          const priceAfterReturnDiscount = priceAfterItemDiscount - returnDiscountAmount;

          // Recalculate tax on the price after return discount
          const taxAmount = Number(((item.taxRate / 100) * priceAfterReturnDiscount));

          // Update item with return discount applied
          item.discountedPrice = priceAfterReturnDiscount;
          item.taxAmount = taxAmount;
          item.finalPrice = priceAfterReturnDiscount + taxAmount;
        }
      }
    }


    // Second pass: Apply cart-level discounts to valid items
    let cartLevelDiscount = 0;

    // Filter valid items for cart-level discount
    const validItems = processedItems // .filter(item => item.isValid) : processedItems;

    // Calculate amounts after item-level and return discounts for valid items
    const itemsAfterItemDiscount = validItems.map(item => ({
      item,
      amountAfterItemDiscount: item.totalPrice - item.discountAmount - (item.returnDiscountAmount || 0)
    }));

    const totalAmountAfterItemDiscount = itemsAfterItemDiscount.reduce(
      (sum, { amountAfterItemDiscount }) => sum + amountAfterItemDiscount,
      0
    );

    // Apply cart-level discount if provided and there are valid items
    if (itemsAfterItemDiscount.length > 0) {
      let hasApplyDiscountAbility = false

      if (discount && discount.value && !discount.discountedBy) {
        validationErrors.push(`Who is apply cart discount?`)
      } else {
        hasApplyDiscountAbility = discount?.discountedBy ? await this.policyService.isHasAbility(discount.discountedBy, ENUM_POLICY_TYPE.POS_DISCOUNT, ENUM_POLICY_ACTION.FULL_ACCESS) : false
      }

      if (discount && discount.type === DiscountType.PERCENTAGE && discount.value && isCartDiscountEnabled) {
        // Apply percentage discount to each valid item individually
        for (const { item, amountAfterItemDiscount } of itemsAfterItemDiscount) {
          try {
            if (!hasApplyDiscountAbility) {
              throw new BadRequestException("User does not have access to apply custom cart discount")
            }
            if (amountAfterItemDiscount > 0) {
              // Calculate cart discount amount for this item (after return discount)
              const itemCartDiscount = Number(((discount.value / 100) * amountAfterItemDiscount));
              const cartDiscountAmount = Math.min(itemCartDiscount, amountAfterItemDiscount);
              cartLevelDiscount += cartDiscountAmount;

              // Apply cart discount to item that already has return discount
              item.cartDiscountAmount = cartDiscountAmount;
              item.cartDiscountType = DiscountType.PERCENTAGE;
              item.cartDiscountValue = discount.value;

              // Calculate final price: original - item discount - return discount - cart discount
              const priceAfterAllDiscounts = item.totalPrice - item.discountAmount - (item.returnDiscountAmount || 0) - cartDiscountAmount;
              const taxAmount = Number(((item.taxRate / 100) * priceAfterAllDiscounts));

              item.discountedPrice = priceAfterAllDiscounts;
              item.taxAmount = taxAmount;
              item.finalPrice = priceAfterAllDiscounts + taxAmount;
            }
          } catch (err) {
            validationErrors.push(err.message)
          }
        }


      } else if (discount && discount.type === DiscountType.FLAT && discount.value && isCartDiscountEnabled) {
        // Distribute flat discount proportionally among valid items
        let allocatedDiscount = 0;

        for (let i = 0; i < itemsAfterItemDiscount.length; i++) {
          const { item, amountAfterItemDiscount } = itemsAfterItemDiscount[i];
          try {
            if (!hasApplyDiscountAbility) {
              throw new BadRequestException("User does not have access to apply custom cart discount")
            }

            if (amountAfterItemDiscount > 0) {
              let cartDiscountAmount = 0;

              // Handle last item to ensure exact total
              if (i === itemsAfterItemDiscount.length - 1) {
                cartDiscountAmount = Math.min(
                  discount.value - allocatedDiscount,
                  amountAfterItemDiscount
                );
              } else {
                const ratio = amountAfterItemDiscount / totalAmountAfterItemDiscount;
                cartDiscountAmount = Number((ratio * discount.value));
                cartDiscountAmount = Math.min(cartDiscountAmount, amountAfterItemDiscount);
              }

              allocatedDiscount += cartDiscountAmount;
              cartLevelDiscount += cartDiscountAmount;

              // Apply cart discount to item that already has return discount
              item.cartDiscountAmount = cartDiscountAmount;
              item.cartDiscountType = DiscountType.FLAT;
              item.cartDiscountValue = discount.value;

              // Calculate final price: original - item discount - return discount - cart discount
              const priceAfterAllDiscounts = item.totalPrice - item.discountAmount - (item.returnDiscountAmount || 0) - cartDiscountAmount;
              const taxAmount = Number(((item.taxRate / 100) * priceAfterAllDiscounts));

              item.discountedPrice = priceAfterAllDiscounts;
              item.taxAmount = taxAmount;
              item.finalPrice = priceAfterAllDiscounts + taxAmount;
            }
          } catch (err) {
            validationErrors.push(err.message)
          }
        }
      }

      // Calculate total tax after applying all discounts
      totalTax = processedItems.reduce((sum, item) => sum + item.taxAmount, 0);

      // Calculate cart-level totals including return discount
      const totalDiscount = itemLevelDiscount + cartLevelDiscount + returnLevelDiscount;

      // Calculate grand total and other values
      const totalAmountAfterGst = subtotal - totalDiscount + totalTax;
      const roundOff = totalAmountAfterGst - Math.floor(totalAmountAfterGst);
      const grandTotal = Math.floor(totalAmountAfterGst);

      // Format values for consistency - use integers for monetary values
      const formattedSubtotal = Number((subtotal)); // 
      const formattedItemDiscount = Number((itemLevelDiscount));
      const formattedCartDiscount = Number((cartLevelDiscount));
      const formattedTotalDiscount = Number((totalDiscount));
      const formattedTax = Number((totalTax));

      return {
        items: processedItems,
        promotion: undefined,
        discountedBy: discount?.discountedBy ?? null,
        cartDiscountType: discount && discount.value > 0 ? discount.type : "",
        cartDiscountValue: discount && discount.value > 0 ? discount.value : 0,
        subTotal: formattedSubtotal,
        subTotalAfterItemLevelDiscount: formattedSubtotal - formattedItemDiscount - returnLevelDiscount,
        itemDiscount: formattedItemDiscount,
        cartDiscount: formattedCartDiscount,
        cartDiscountAmount: formattedCartDiscount, // Using cartDiscount for cartDiscountAmount
        totalDiscount: formattedTotalDiscount,
        discount: formattedTotalDiscount, // For backward compatibility
        totalTax: formattedTax,
        totalGstValue: formattedTax,
        totalAmountAfterGst,
        roundOff,
        grandTotal,
        amountPaid: totalAmountAfterGst,
        returnDiscount: totalEffectiveReturnUnitPrice,
        returnItems: [...returnItemsDetails, ...returnCustomPackageItemDetails],
        returnDetails,
        returnCustomPackageDetails,
        validationErrors: validationErrors.length > 0 ? [...new Set(validationErrors)] : [],
      };
    }
  }


  /**
   * Process a single cart item
   */
  private async processCartItem(
    item: any,
    organizationId: string,
    isMember: boolean,
    facilityId?: string,
    autoApplyPromotion?: boolean
  ): Promise<CartItemDetailDto> {
    const { itemType, itemId, quantity, variantId, promotionId, discount, promotionLabel, promotionLabelKey } = item;
    let itemDetail: CartItemDetailDto;

    // Process item based on type
    switch (itemType) {
      case ENUM_ITEM_TYPE.SERVICE:
        itemDetail = await this.processServiceItem({
          itemId,
          quantity,
          organizationId,
          isMember,
          promotionId,
          facilityId,
          additionalDiscount: discount,
          autoApplyPromotion,
          promotionLabel,
          promotionLabelKey
        });
        break;

      case ENUM_ITEM_TYPE.PRODUCT:
        itemDetail = await this.processProductItem({
          itemId,
          quantity,
          variantId,
          organizationId,
          isMember,
          promotionId,
          facilityId,
          additionalDiscount: discount,
          autoApplyPromotion
        });
        break;

      case ENUM_ITEM_TYPE.CUSTOM_PACKAGE:
        itemDetail = await this.processCustomPackageItem({
          itemId,
          quantity,
          organizationId,
          isMember,
          promotionId,
          facilityId,
          additionalDiscount: discount
        });
        break;

      default:
        throw new BadRequestException(`Invalid item type: ${itemType}`);
    }

    return itemDetail;
  }

  /**
   * Process a service item
   */
  private async processServiceItem(params: {
    itemId: string;
    quantity: number;
    organizationId: string;
    isMember: boolean;
    promotionId?: string;
    facilityId?: string;
    discountedBy?: string;
    additionalDiscount?: { type: DiscountType; value: number, discountedBy: string };
    autoApplyPromotion?: boolean;
    promotionLabel?: string,
    promotionLabelKey?: string
  }): Promise<CartItemDetailDto> {
    const { itemId, quantity, organizationId, isMember, promotionId, facilityId, additionalDiscount, autoApplyPromotion, promotionLabel,
      promotionLabelKey } = params;
    const validationErrors: string[] = [];
    let isValid = true;

    // Fetch service details
    const service = await this.pricingModel.findOne({
      _id: new Types.ObjectId(itemId),
      organizationId: new Types.ObjectId(organizationId),
      isActive: true,
    });

    if (!service) {
      throw new NotFoundException(`One of the selected service/product items not found or inactive.`);
    }

    let promotion: PromotionDocument = null;
    let itemDiscount = null;

    // Get promotion details if a specific promotion ID is provided
    if (promotionId || (service.promotion && !additionalDiscount && autoApplyPromotion)) {
      try {
        // if (!(service.promotion && service.promotion.equals(promotionId)) ) {
        // Validate discounted by
        // }
        promotion = await this.validateItemPromotion({
          itemType: ENUM_ITEM_TYPE.SERVICE,
          organizationId,
          isMember,
          promotionId: promotionId ?? service.promotion?.toString(),
          facilityId,
          item: service
        });

        itemDiscount = {
          type: promotion.type,
          value: promotion.value as number,
        };
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without promotion discount
      }
    }
    else if (additionalDiscount && !additionalDiscount.discountedBy && additionalDiscount.value) {
      isValid = false;
      validationErrors.push(`Who is apply item discount?`)
    }
    // Apply additional discount if provided and no promotion is applied
    else if (additionalDiscount && additionalDiscount.discountedBy && additionalDiscount.value) {
      try {
        const hasApplyDiscountAbility = await this.policyService.isHasAbility(additionalDiscount.discountedBy, ENUM_POLICY_TYPE.POS_DISCOUNT, ENUM_POLICY_ACTION.FULL_ACCESS)
        if (!hasApplyDiscountAbility) {
          throw new BadRequestException("User does not have access to apply custom item discount")
        }
        // Validate additional discount
        if (additionalDiscount.type === DiscountType.FLAT &&
          (additionalDiscount.value < 0 || additionalDiscount.value > (service.price as number))) {
          throw new BadRequestException(
            `Invalid discount value. Discount value ${additionalDiscount.value} cannot be less than 0 or greater than ${service.price} for this item`
          );
        }

        if (additionalDiscount.type === DiscountType.PERCENTAGE &&
          (additionalDiscount.value < 0 || additionalDiscount.value > 100)) {
          throw new BadRequestException(
            `Invalid discount value. Discount value ${additionalDiscount.value} cannot be less than 0 or greater than 100 for a percentage discount`
          );
        }

        itemDiscount = additionalDiscount;
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without additional discount
      }
    }

    if (!this.activeTimeFrameService.isActive(service.activeTimeFrames)) {
      isValid = false;
      throw new BadRequestException(`Service ${service.name} is available only during specific time frames. Please look over service availability.`);
    }


    // Calculate item pricing
    const pricing = this.calculateItemPricing({
      quantity,
      price: service.price as number,
      taxRate: service.tax as number,
      itemDiscount,
    });

    return {
      _id: itemId,
      organizationId,
      name: service.name,
      price: pricing.price, // actual price
      isSellOnline: service.isSellOnline ? true : true,
      tax: Number(service.tax),
      expiredInDays: service.expiredInDays,
      hsnOrSacCode: service.hsnOrSacCode,
      durationUnit: service.durationUnit,
      promotion: promotion ? {
        _id: promotion._id.toString(),
        name: promotion.name,
        couponCode: promotion.couponCode,
        description: promotion.description,
        type: promotion.type,
        value: promotion.value,
        target: promotion.target,
        itemType: promotion.itemType,
        isActive: promotion.isActive,
        promotionLabel: promotion?.promotionLabel

      } : null,
      isActive: service.isActive ? true : true,
      pricingIds: [],
      isBundledPricing: service.isBundledPricing ? true : false,
      itemType: ENUM_ITEM_TYPE.SERVICE,
      quantity,
      totalPrice: pricing.totalPrice,
      discountAmount: pricing.discountAmount,
      discountType: pricing.discountType || DiscountType.FLAT,
      discountValue: pricing.discountValue || 0,
      discountedBy: additionalDiscount && additionalDiscount.discountedBy ? additionalDiscount && additionalDiscount.discountedBy : null,
      cartDiscountAmount: pricing.cartDiscountAmount,
      cartDiscountType: pricing.cartDiscountType,
      cartDiscountValue: pricing.cartDiscountValue,
      taxRate: pricing.taxRate,
      taxAmount: pricing.taxAmount,
      discountedPrice: pricing.discountedPrice,
      finalPrice: pricing.finalPrice,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
      isValid,
      promotionLabel,
      promotionLabelKey
    };
  }

  /**
   * Process a product item
   */
  private async processProductItem(params: {
    itemId: string;
    quantity: number;
    variantId?: string;
    organizationId: string;
    isMember: boolean;
    promotionId?: string;
    facilityId?: string;
    additionalDiscount?: { type: DiscountType; value: number, discountedBy: string };
    autoApplyPromotion?: boolean;
  }): Promise<CartItemDetailDto> {
    const { itemId, quantity, variantId, organizationId, isMember, promotionId, facilityId, additionalDiscount, autoApplyPromotion } = params;
    const validationErrors: string[] = [];
    let isValid = true;

    const inventoryQuery: any = {
      productId: new Types.ObjectId(itemId),
      organizationId: new Types.ObjectId(organizationId),
    };
    if (variantId) {
      inventoryQuery.productVariantId = new Types.ObjectId(variantId);
    }

    // Fetch product details
    const inventory: (Omit<InventoryDocument, 'productId' | 'productVariantId'> & { productId: Pick<ProductDocument, 'name' | 'gst' | '_id'>, productVariantId: Pick<ProductVariantDocument, 'title' | '_id'> }) = await this.inventoryModel.findOne(inventoryQuery);

    if (!inventory) {
      throw new NotFoundException(`Product not found in inventory`);
    }

    await inventory.populate([
      {
        path: 'productId',
        select: 'name gst',
      },
      {
        path: 'productVariantId',
        select: 'title'
      }
    ])
    if (!inventory.productId || (variantId && !inventory.productVariantId)) {
      throw new NotFoundException(`Product not found`);
    }

    // Check stock
    if (inventory.quantity && Number(inventory.quantity) < quantity) {
      validationErrors.push(`Insufficient stock for product`);
      isValid = false;
    }

    // Get promotion details if a specific promotion ID is provided
    let promotion = null;
    let itemDiscount = null;

    if (promotionId || (inventory.promotion && !additionalDiscount && autoApplyPromotion)) {
      try {
        promotion = await this.validateItemPromotion({
          itemType: ENUM_ITEM_TYPE.PRODUCT,
          organizationId,
          isMember,
          promotionId,
          facilityId,
          item: inventory
        });

        itemDiscount = {
          type: promotion.type,
          value: promotion.value as number
        };
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without promotion discount
      }
    }
    // Apply additional discount if provided and no promotion is applied
    else if (additionalDiscount && additionalDiscount.discountedBy) {
      try {
        // Validate additional discount
        if (additionalDiscount.type === DiscountType.FLAT &&
          (additionalDiscount.value < 0 || additionalDiscount.value > Number(inventory.salePrice))) {
          throw new BadRequestException(
            `Invalid discount value. Discount value ${additionalDiscount.value} cannot be less than 0 or greater than ${inventory.salePrice} for this item`
          );
        }

        if (additionalDiscount.type === DiscountType.PERCENTAGE &&
          (additionalDiscount.value < 0 || additionalDiscount.value > 100)) {
          throw new BadRequestException(
            `Invalid discount value. Discount value ${additionalDiscount.value} cannot be less than 0 or greater than 100 for a percentage discount`
          );
        }

        itemDiscount = additionalDiscount;
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without additional discount
      }
    }

    // Calculate item pricing
    const price = Number(inventory.salePrice);
    const taxRate = inventory?.productId?.gst ? Number(inventory?.productId?.gst) : 0;

    const pricing = this.calculateItemPricing({
      quantity,
      price,
      taxRate,
      itemDiscount,
    });

    return {
      _id: itemId,
      organizationId,
      name: inventory?.productVariantId?.title as string || inventory?.productId?.name as string,
      price: pricing.price,
      isSellOnline: true,
      tax: 0,
      hsnOrSacCode: '',
      promotion: promotion ? {
        _id: promotion._id.toString(),
        name: promotion.name,
        couponCode: promotion.couponCode,
        description: promotion.description,
        type: promotion.type,
        value: promotion.value,
        target: promotion.target,
        itemType: promotion.itemType,
        isActive: promotion.isActive,
      } : null,
      additionalDiscount: additionalDiscount || null,
      isActive: true,
      pricingIds: [],
      isBundledPricing: false,
      itemType: ENUM_ITEM_TYPE.PRODUCT,
      quantity,
      totalPrice: pricing.totalPrice,
      discountAmount: pricing.discountAmount,
      discountType: pricing.discountType || DiscountType.FLAT,
      discountValue: pricing.discountValue || 0,
      cartDiscountAmount: pricing.cartDiscountAmount,
      cartDiscountType: pricing.cartDiscountType,
      cartDiscountValue: pricing.cartDiscountValue,
      taxRate: pricing.taxRate,
      taxAmount: pricing.taxAmount,
      discountedPrice: pricing.discountedPrice,
      finalPrice: pricing.finalPrice,
      variantId,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
      isValid,
    };
  }

  /**
   * Process a custom package item
   */
  private async processCustomPackageItem(params: {
    itemId: string;
    quantity: number;
    organizationId: string;
    isMember: boolean;
    promotionId?: string;
    facilityId?: string;
    additionalDiscount?: { type: DiscountType; value: number };
  }): Promise<CartItemDetailDto> {
    const { itemId, quantity, organizationId, isMember, promotionId, facilityId, additionalDiscount } = params;
    const validationErrors: string[] = [];
    let isValid = true;

    // Fetch custom package details
    const customPackage = await this.customPackageModel.findOne({
      _id: new Types.ObjectId(itemId),
      organizationId: new Types.ObjectId(organizationId),
      isActive: true,
    });

    if (!customPackage) {
      throw new NotFoundException(`Custom package with ID ${itemId} not found or inactive`);
    }

    // Get promotion details if a specific promotion ID is provided
    let promotion = null;
    let itemDiscount = null;

    if (promotionId) {
      try {
        promotion = await this.validateItemPromotion({
          itemType: ENUM_ITEM_TYPE.CUSTOM_PACKAGE,
          organizationId,
          isMember,
          promotionId,
          facilityId,
          item: customPackage
        });

        itemDiscount = {
          type: promotion.type,
          value: promotion.value as number
        };
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without promotion discount
      }
    }
    // Apply additional discount if provided and no promotion is applied
    else if (additionalDiscount) {
      try {
        const packagePrice = Number(customPackage.unitPrice || 0);

        // Validate additional discount
        if (additionalDiscount.type === DiscountType.FLAT &&
          (additionalDiscount.value < 0 || additionalDiscount.value > packagePrice)) {
          throw new BadRequestException(
            `Invalid discount value. Discount value ${additionalDiscount.value} cannot be less than 0 or greater than ${packagePrice} for this item`
          );
        }

        if (additionalDiscount.type === DiscountType.PERCENTAGE &&
          (additionalDiscount.value < 0 || additionalDiscount.value > 100)) {
          throw new BadRequestException(
            `Invalid discount value. Discount value ${additionalDiscount.value} cannot be less than 0 or greater than 100 for a percentage discount`
          );
        }

        itemDiscount = additionalDiscount;
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without additional discount
      }
    }
    // Use package's own discount if available and no other discount is applied
    else if (customPackage.discount) {
      try {
        itemDiscount = {
          type: customPackage.discount.type,
          value: customPackage.discount.value as number
        };
      } catch (error) {
        isValid = false;
        validationErrors.push(error.message);
        // Continue without package discount
      }
    }

    // Calculate item pricing
    const price = Number(customPackage.unitPrice || 0);
    const taxRate = customPackage.tax ? Number(customPackage.tax) : 0;

    const pricing = this.calculateItemPricing({
      quantity,
      price,
      taxRate,
      itemDiscount,
    });
    return {
      _id: itemId,
      organizationId,
      name: customPackage.name || 'Unknown Package',
      price: pricing.price,
      isSellOnline: true,
      tax: Number(customPackage.tax || 0),
      hsnOrSacCode: customPackage.hsnOrSacCode || '',
      promotion: promotion ? {
        _id: promotion._id.toString(),
        name: promotion.name,
        couponCode: promotion.couponCode,
        description: promotion.description,
        type: promotion.type,
        value: promotion.value,
        target: promotion.target,
        itemType: promotion.itemType,
        isActive: promotion.isActive,
      } : null,
      additionalDiscount: additionalDiscount || null,
      isActive: true,
      pricingIds: [],
      isBundledPricing: false,
      itemType: ENUM_ITEM_TYPE.CUSTOM_PACKAGE,
      quantity,
      totalPrice: pricing.totalPrice,
      discountAmount: pricing.discountAmount,
      discountType: pricing.discountType || DiscountType.FLAT,
      discountValue: pricing.discountValue || 0,
      cartDiscountAmount: pricing.cartDiscountAmount,
      cartDiscountType: pricing.cartDiscountType,
      cartDiscountValue: pricing.cartDiscountValue,
      taxRate: pricing.taxRate,
      taxAmount: pricing.taxAmount,
      discountedPrice: pricing.discountedPrice,
      finalPrice: pricing.finalPrice,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
      isValid,
    };
  }

  /**
   * Validate a promotion for an item
   */
  async validateItemPromotion(params: {
    itemType: ENUM_ITEM_TYPE;
    organizationId: string;
    isMember: boolean;
    promotionId: string;
    facilityId?: string;
    item?: any;
  }): Promise<PromotionDocument> {
    const { itemType, organizationId, isMember, promotionId, facilityId, item } = params;
    const currentDate = new Date();

    const promise: any[] = [
      this.promotionService.findOne({
        _id: new Types.ObjectId(promotionId),
        isActive: true,
        organizationId: new Types.ObjectId(organizationId),
        itemType: itemType,
        $or: [
          { startDate: null },
          { endDate: null },
          {
            $and: [
              { startDate: { $lte: currentDate } },
              { endDate: { $gte: currentDate } }
            ]
          }
        ],
      }),
      item ? this.promotionItemService.getTotal({
        promotion: new Types.ObjectId(promotionId),
        item: item._id,
        organizationId: new Types.ObjectId(organizationId),
        itemType: itemType,
      }) : Promise.resolve(1), // If no item is provided, assume it's valid
      // item ? this.promotionItemService.getTotal({
      //   promotion: new Types.ObjectId(promotionId),
      //   item: item.productId?._id,
      //   organizationId: new Types.ObjectId(organizationId),
      //   itemType: itemType,
      // }) : Promise.resolve(1), // If no item is provided, assume it's valid
    ];

    const [promotion, total] = await Promise.all(promise);
    if (!promotion) {
      throw new NotFoundException(`Promotion not found for item ${item ? item?.name ?? "" : ""}`);
    }

    if (promotion.facilityIds?.length) {
      const facilityIdObj = new Types.ObjectId(facilityId);
      if (!promotion.facilityIds.some((id: Types.ObjectId) => id.equals(facilityIdObj))) {
        throw new BadRequestException(`Promotion is not valid for this facility`);
      }
    }

    if (promotion.target === ENUM_PROMOTION_TARGET.MEMBERS_ONLY && !isMember) {
      throw new BadRequestException(`Promotion is only for members`);
    }

    if (item && !total) {
      throw new BadRequestException(`This promotion is not valid for ${item.name || 'this'} ${itemType}`);
    }

    return promotion;
  }

  /**
   * Re-validate a cart before purchase
   */
  async revalidateCart(cartRequest: CartRequestDto): Promise<CartResponseDto> {
    return this.processCart(cartRequest, true);
  }



  /**
   * Calculate pricing for a single item including discounts and taxes
   */
  private calculateItemPricing(params: {
    quantity: number;
    price: number;
    taxRate: number;
    itemDiscount?: { type: DiscountType; value: number };
    cartDiscount?: { type: string; value: number; amount: number };
  }) {
    const { quantity, price, taxRate, itemDiscount, cartDiscount } = params;

    // Calculate total price
    const totalPrice = price * quantity;

    // Initialize discount values
    let itemDiscountAmount = 0;
    let itemDiscountType: string | undefined;
    let itemDiscountValue: number | undefined;

    let cartDiscountAmount = cartDiscount?.amount || 0;
    let cartDiscountType = cartDiscount?.type;
    let cartDiscountValue = cartDiscount?.value;

    // Apply item-level discount if available
    if (itemDiscount && itemDiscount.type && itemDiscount.value > 0) {
      if (itemDiscount.type === DiscountType.PERCENTAGE) {
        // Calculate percentage discount
        itemDiscountAmount = Number(((itemDiscount.value / 100) * totalPrice));
        itemDiscountType = DiscountType.PERCENTAGE;
        itemDiscountValue = itemDiscount.value;
      } else if (itemDiscount.type === DiscountType.FLAT) {
        // Calculate flat discount
        itemDiscountAmount = Math.min(itemDiscount.value * quantity, totalPrice);
        itemDiscountType = DiscountType.FLAT;
        itemDiscountValue = itemDiscount.value;
      }

      // Ensure discount doesn't exceed total price
      itemDiscountAmount = Math.min(itemDiscountAmount, totalPrice);
    }

    // Calculate amount after item-level discount
    const amountAfterItemDiscount = totalPrice - itemDiscountAmount;

    // Ensure cart discount doesn't exceed remaining amount
    cartDiscountAmount = Math.min(cartDiscountAmount || 0, amountAfterItemDiscount);

    // Calculate total discount
    const totalDiscountAmount = itemDiscountAmount + cartDiscountAmount;

    // Calculate tax amount (on price after all discounts)
    const taxableAmount = totalPrice - totalDiscountAmount;
    const taxAmount = Number(((taxRate / 100) * taxableAmount));

    // Calculate final price
    const finalPrice = totalPrice - totalDiscountAmount + taxAmount;

    return {
      price,
      totalPrice,
      discountAmount: itemDiscountAmount,
      discountType: itemDiscountType,
      discountValue: itemDiscountValue,
      cartDiscountAmount,
      cartDiscountType,
      cartDiscountValue,
      totalDiscountAmount,
      taxRate,
      taxAmount,
      discountedPrice: amountAfterItemDiscount,
      finalPrice,
    };
  }

  /**
   * Process return purchases and calculate their effective value
   */
  private async processReturnPurchases(returnPurchaseIds: string[], organizationId: string, userId: string) {
    const returnPurchases = await this.purchaseModel.aggregate([
      {
        $match: {
          _id: { $in: returnPurchaseIds.map(id => new Types.ObjectId(id)) },
          organizationId: new Types.ObjectId(organizationId),
          userId: new Types.ObjectId(userId),
          isExpired: false,
          endDate: { $gte: new Date() },
          startDate: { $lte: new Date() },
          isActive: { $ne: false },
          bundledPricingId: { $exists: false },
          isExchanged: { $ne: true },
          exchangedInvoiceId: { $exists: false },
          sharePass: { $ne: true }
        }
      },
      {
        $lookup: {
          from: "invoices",
          localField: "invoiceId",
          foreignField: "_id",
          as: "invoiceDetails"
        }
      },
      {
        $unwind: {
          path: "$invoiceDetails",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $addFields: {
          matchedPurchaseItem: {
            $arrayElemAt: [
              {
                $filter: {
                  input: "$invoiceDetails.purchaseItems",
                  as: "item",
                  cond: {
                    $eq: ["$$item.packageId", "$packageId"]
                  }
                }
              },
              0
            ]
          }
        }
      },
      {
        $lookup: {
          from: "pricings",
          localField: "packageId",
          foreignField: "_id",
          as: "packageDetails"
        }
      },
      {
        $unwind: {
          path: "$packageDetails",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $project: {
          _id: 1,
          invoiceId: 1,
          packageId: 1,
          packageName: "$packageDetails.name",
          price: "$packageDetails.price",
          tax: "$packageDetails.tax",
          hsnOrSacCode: "$packageDetails.hsnOrSacCode",
          expiredInDays: "$packageDetails.expiredInDays",
          durationUnit: "$packageDetails.durationUnit",
          "matchedPurchaseItem.unitPrice": 1,
          "matchedPurchaseItem.quantity": 1,
          "matchedPurchaseItem.discountExcludeCart": 1,
          "matchedPurchaseItem.discountIncludeCart": 1,
          paymentStatus: "$invoiceDetails.paymentStatus"
        }
      }
    ]);

    if (returnPurchases.length !== returnPurchaseIds.length) {
      throw new BadRequestException(
        'Some selected returns are not eligible (expired or inactive)'
      );
    }

    const totalReturnValue = returnPurchases.reduce((total, purchase) => {
      const item = purchase.matchedPurchaseItem;
      const unitPrice = item?.unitPrice || 0;
      const discountExcludeCart = Number(item?.discountExcludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
      const discountIncludeCart = Number(item?.discountIncludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;

      const effectiveUnitPrice = purchase.paymentStatus === PaymentStatus.COMPLETED
        ? unitPrice - (discountExcludeCart + discountIncludeCart)
        : 0;
      return total + effectiveUnitPrice;
    }, 0);

    // Format return purchases as cart items
    const returnItemsDetails = returnPurchases.map(purchase => {
      const item = purchase.matchedPurchaseItem;
      const unitPrice = item?.unitPrice || 0;
      const discountExcludeCart = Number(item?.discountExcludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
      const discountIncludeCart = Number(item?.discountIncludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
      const effectiveUnitPrice = purchase.paymentStatus === PaymentStatus.COMPLETED
        ? unitPrice - (discountExcludeCart + discountIncludeCart)
        : 0;

      return {
        _id: purchase.packageId.toString(),
        purchaseId: purchase._id,
        organizationId: organizationId,
        name: purchase.packageName || 'Returned Package',
        price: unitPrice,
        isSellOnline: true,
        tax: Number(purchase.tax || 0),
        expiredInDays: purchase.expiredInDays,
        hsnOrSacCode: purchase.hsnOrSacCode || '',
        durationUnit: purchase.durationUnit,
        promotion: null,
        isActive: false, // Mark as inactive since it's being returned
        pricingIds: [],
        isBundledPricing: false,
        itemType: 'SERVICE' as any,
        quantity: 1,
        totalPrice: effectiveUnitPrice,
        discountAmount: discountExcludeCart + discountIncludeCart,
        discountType: 'FLAT' as any,
        discountValue: discountExcludeCart + discountIncludeCart,
        taxRate: Number(purchase.tax || 0),
        taxAmount: 0, // No additional tax on returns
        cartDiscountAmount: 0,
        cartDiscountType: undefined,
        cartDiscountValue: 0,
        discountedPrice: effectiveUnitPrice,
        finalPrice: effectiveUnitPrice,
        isValid: true,
        validationErrors: []
      };
    });

    return {
      returnPurchases,
      totalReturnValue,
      returnItemsDetails
    };
  }

  /**
   * Process return purchases and calculate their effective value
   */
  private async processReturnCustomItemPurchases(returnPurchaseIds: string[], organizationId: string, userId: string) {
    const returnPurchases = await this.invoiceModel.aggregate([
      {
        $match: {
          'customPackageItems._id': { $in: returnPurchaseIds.map(id => new Types.ObjectId(id)) },
          organizationId: new Types.ObjectId(organizationId),
          userId: new Types.ObjectId(userId)
        }
      },
      {
        $unwind: {
          path: "$customPackageItems",
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $match: {
          $or: [
            {
              'customPackageItems.isReturned': false
            },
            {
              'customPackageItems.isReturned': {
                $exists: false
              }
            }
          ]
        }
      },
      {
        $project: {
          _id: "$customPackageItems._id",
          invoiceId: '$_id',
          packageId: "$customPackageItems.customPackageId",
          packageName: "$customPackageItems.packageName",
          price: "$customPackageItems.unitPrice",
          tax: "$customPackageItems.tax",
          hsnOrSacCode: "$customPackageItems.hsnOrSacCode",
          "customPackageItems.unitPrice": 1,
          "customPackageItems.quantity": 1,
          "customPackageItems.discountExcludeCart": 1,
          "customPackageItems.discountIncludeCart": 1,
          paymentStatus: "$paymentStatus"
        }
      }
    ]);

    if (returnPurchases.length !== returnPurchaseIds.length) {
      throw new BadRequestException(
        'Some selected returns item (custom items) are not eligible (expired or inactive)'
      );
    }

    const totalReturnValue = returnPurchases.reduce((total, purchase) => {
      const item = purchase.customPackageItems;
      const unitPrice = item?.unitPrice || 0;
      const discountExcludeCart = Number(item?.discountExcludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
      const discountIncludeCart = Number(item?.discountIncludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;

      const effectiveUnitPrice = purchase.paymentStatus === PaymentStatus.COMPLETED
        ? unitPrice - (discountExcludeCart + discountIncludeCart)
        : 0;
      return total + effectiveUnitPrice;
    }, 0);

    // Format return purchases as cart items
    const returnItemsDetails = returnPurchases.map(purchase => {
      const item = purchase.customPackageItems;
      const unitPrice = item?.unitPrice || 0;
      const discountExcludeCart = Number(item?.discountExcludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
      const discountIncludeCart = Number(item?.discountIncludeCart) / (item?.quantity >= 1 ? item?.quantity : 1) || 0;
      const effectiveUnitPrice = purchase.paymentStatus === PaymentStatus.COMPLETED
        ? unitPrice - (discountExcludeCart + discountIncludeCart)
        : 0;

      return {
        _id: purchase.packageId.toString(),
        purchaseId: purchase._id,
        organizationId: organizationId,
        name: purchase.packageName || 'Returned Package',
        price: unitPrice,
        isSellOnline: true,
        tax: Number(purchase.tax || 0),
        expiredInDays: purchase.expiredInDays,
        hsnOrSacCode: purchase.hsnOrSacCode || '',
        durationUnit: purchase.durationUnit,
        promotion: null,
        isActive: false, // Mark as inactive since it's being returned
        pricingIds: [],
        isBundledPricing: false,
        itemType: ENUM_ITEM_TYPE.CUSTOM_PACKAGE as any,
        quantity: 1,
        totalPrice: effectiveUnitPrice,
        discountAmount: discountExcludeCart + discountIncludeCart,
        discountType: DiscountType.FLAT as any,
        discountValue: discountExcludeCart + discountIncludeCart,
        taxRate: Number(purchase.tax || 0),
        taxAmount: 0, // No additional tax on returns
        cartDiscountAmount: 0,
        cartDiscountType: undefined,
        cartDiscountValue: 0,
        discountedPrice: effectiveUnitPrice,
        finalPrice: effectiveUnitPrice,
        isValid: true,
        validationErrors: []
      };
    });

    return {
      returnPurchases,
      totalReturnValue,
      returnItemsDetails
    };
  }
}
