import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';
import { PermissionService } from 'src/policy/services/permission.service';
import { schedulingPermissions } from '../data/permissions/scheduling.data';
import { pricingPermissions } from '../data/permissions/pricing.data';
import { clientsPermissions } from '../data/permissions/clients.data';
import { facilityPermissions } from '../data/permissions/facility.data';
import { staffPermissions } from '../data/permissions/staff.data';
import { roomsPermissions } from '../data/permissions/rooms.data';
import { purchasePermissions } from '../data/permissions/purchase.data';
import { membershipPermissions } from '../data/permissions/membership.data';
import { merchandisePermissions } from '../data/permissions/merchandise.data';
import { announcementPermissions } from '../data/permissions/announcements.data';
import { payRatePermissions } from '../data/permissions/pay-rate.data';
import { promotionsPermissions } from '../data/permissions/promotions.data';
import { transactionPermissions } from '../data/permissions/transaction.data';
import { policyPermissions } from '../data/permissions/policy.data';
import { featurePermissions } from '../data/permissions/feature.data';
import { organizationPermissions } from '../data/permissions/organization.data';

@Injectable()
export class MigrationPermissionSeed {
    constructor(private readonly permissionService: PermissionService) {}

    @Command({
        command: 'seed:permission',
        describe: 'seed permissions',
    })
    async seeds(): Promise<void> {
        const data: PermissionCreateRequestDto[] = [
            ...schedulingPermissions,
            ...pricingPermissions,
            ...clientsPermissions,
            ...facilityPermissions,
            ...staffPermissions,
            ...roomsPermissions,
            ...purchasePermissions,
            ...membershipPermissions,
            ...merchandisePermissions,
            ...announcementPermissions,
            ...payRatePermissions,
            ...promotionsPermissions,
            ...transactionPermissions,
            ...policyPermissions,
            ...featurePermissions,
            ...organizationPermissions,
            ...purchasePermissions
        ];

        try {
            const updatedPromises = data.map(async (permission) => {
                const { type, ...updateData} = permission;
                return await this.permissionService.updateMany({ type: type }, updateData, { upsert: true, withDeleted: true });
            });
            await Promise.all(updatedPromises);
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }

    @Command({
        command: 'remove:role',
        describe: 'remove roles',
    })
    async remove(): Promise<void> {
        try {
            await this.permissionService.deleteMany({});
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }
}
