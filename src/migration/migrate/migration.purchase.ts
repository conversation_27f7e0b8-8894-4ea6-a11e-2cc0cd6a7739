import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import {
    ENUM_ROLE_TYPE,
} from 'src/role/enums/role.enum';
import { RoleService } from 'src/role/services/role.service';
import { Purchase, PurchaseDocument } from 'src/users/schemas/purchased-packages.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class MigrationPurchase {
    constructor(
        @InjectModel(Purchase.name) private PurchaseModel: Model<PurchaseDocument>,
    ) { }

    @Command({
        command: 'purchase:consumers',
        describe: 'Add consumers to purchase',
    })
    async seeds(): Promise<void> {

        try {
            const batchSize = 10000;
            let skip = 0;
            let hasMore = true;

            while (hasMore) {
                const purchases = await this.PurchaseModel.find({ consumers: { $exists: false } }, { userId: 1, consumers: 1 })
                    .limit(batchSize)
                    .skip(skip);

                if (purchases.length === 0) {
                    hasMore = false;
                    break;
                }

                const bulkOps = purchases
                    .filter(purchase => !purchase.consumers?.length)
                    .map(purchase => ({
                        updateOne: {
                            filter: { _id: purchase._id },
                            update: { $set: { consumers: [purchase.userId] } }
                        }
                    }));

                if (bulkOps.length > 0) {
                    await this.PurchaseModel.bulkWrite(bulkOps);
                }
                console.log(`Processed ${bulkOps.length} purchases`);
                skip += batchSize;
            }
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }
}
