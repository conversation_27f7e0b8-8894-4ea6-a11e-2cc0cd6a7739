import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { <PERSON>A<PERSON>y, IsBoolean, IsDate, IsEmpty, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min, ValidateIf, ValidateNested } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { RecurringScheduleDTO } from "./schedule-week.dto";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";


export class CreateSchedulingDto {
    @ApiProperty({
        description: "Organization ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    organizationId: string;

    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @ValidateIf(val => [ClassType.PERSONAL_APPOINTMENT, ClassType.COURSES].includes(val.classType))
    @IsNotEmpty()
    trainerId?: string;

    @ApiProperty({
        description: "Client ID for whom the appointment is made",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    clientId: string;

    @ApiProperty({
        description: "Purchase Id selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    purchaseId: string;

    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Service ID if any service is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    subType?: string;

    @ApiProperty({
        description: "Pay-rate id",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    payRate?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    serviceCategory: string;

    @ApiProperty({
        description: "Room Id for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    roomId?: string;

    @ApiProperty({

        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsOptional()
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    dateRange?: DateRange;

    @ApiProperty({
        description: "The schedule for scheduling",
        type: RecurringScheduleDTO,
        required: true
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "Schedule is required" })
    schedule: RecurringScheduleDTO;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
        required: true,
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @IsNotEmpty()
    @Type(() => Number)
    @IsNumber()
    @Min(15)
    duration: number;

    @ApiProperty({
        description: "Date of the appointment",
        type: Date,
        example: new Date(),
        required: true
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @IsNotEmpty({ message: "Date is required" })
    @Type(() => Date)
    date: Date;

    @ApiProperty({
        description: "Start Date for Scheduling",
        type: Date,
        example: new Date(),
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty()
    @Type(() => Date)
    startDate: Date;

    @ApiProperty({
        description: "End Date for Scheduling",
        type: Date,
        example: new Date(),
    })
    @ValidateIf((obj) => obj.markType === MarkAvailabilityType.CUSTOM)
    @IsNotEmpty()
    @Type(() => Date)
    endDate: Date;

    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;

    @ApiProperty({
        description: "Flag to send confirmation for the appointment",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    sendConfirmation?: boolean;

    @ApiProperty({
        description: "Note for appointment",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: "Check-in to schedule",
        example: false,
    })
    @IsOptional()
    @Type(() => Boolean)
    @IsBoolean()
    checkIn?: boolean = false;

    @ApiProperty({
        description: "MarkType for Scheduling",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
        required: false,
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "MarkType is required" })
    @IsEnum(MarkAvailabilityType, { message: "MarkType must be a valid enum value" })
    markType?: MarkAvailabilityType;
}

export class SchedulingParamDto {
    @ApiProperty({ description: "Organization ID", example: "615c2f8e2c1ae9123cbb3c1b" })
    @IsNotEmpty()
    organizationId: string;

    @ApiProperty({ description: "Facility ID", example: "615c2f8e2c1ae9123cbb3c1b" })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({ description: "Client ID", example: "615c2f8e2c1ae9123cbb3c1b" })
    @IsNotEmpty()
    clientId: string;

    @ApiProperty({ description: "Class type", enum: ClassType, example: ClassType.PERSONAL_APPOINTMENT })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;
}

export class SchedulingQueryDto {
    @ApiProperty({ description: "Purchase Id", example: "615c2f8e2c1ae9123cbb3c1b" })
    @IsNotEmpty()
    purchaseId: string;

    @ApiProperty({ description: "Service Category Id", example: "615c2f8e2c1ae9123cbb3c1b" })
    @IsNotEmpty()
    serviceCategory: string;

    @ApiProperty({ description: "Trainer Id", required: false })
    @IsOptional()
    trainerId?: string;

    @ApiProperty({ description: "Pay Rate Id", required: false })
    @IsOptional()
    payRate?: string;

    @ApiProperty({ description: "Room Id", required: false })
    @IsOptional()
    roomId?: string;

    @ApiProperty({ description: "Subtype Id", required: false })
    @IsOptional()
    subType?: string;

    @ApiProperty({ description: "Date Range", enum: DateRange, example: DateRange.SINGLE })
    @IsOptional()
    @IsEnum(DateRange)
    dateRange?: DateRange;

    @ApiProperty({ description: "Schedule", type: RecurringScheduleDTO, required: false })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty()
    schedule?: RecurringScheduleDTO;

    @ApiProperty({ description: "Duration (minutes)", example: 60 })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @Type(() => Number)
    @IsNumber()
    @Min(15)
    duration?: number;

    @ApiProperty({ description: "Date (for SINGLE)", example: new Date() })
    @ValidateIf((obj) => obj.dateRange === DateRange.SINGLE)
    @Type(() => Date)
    date?: Date;

    @ApiProperty({ description: "Start Date", example: new Date() })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @Type(() => Date)
    startDate?: Date;

    @ApiProperty({ description: "End Date", example: new Date() })
    @ValidateIf((obj) => obj.markType === MarkAvailabilityType.CUSTOM)
    @Type(() => Date)
    endDate?: Date;

    @ApiProperty({ description: "From time (HH:mm)", example: "08:00" })
    @IsOptional()
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/)
    from?: string;

    @ApiProperty({ description: "To time (HH:mm)", example: "17:00" })
    @IsOptional()
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/)
    to?: string;

    @ApiProperty({ description: "Send Confirmation", example: false })
    @IsOptional()
    @Type(() => Boolean)
    @IsBoolean()
    sendConfirmation?: boolean;

    @ApiProperty({ description: "Notes", required: false })
    @IsOptional()
    notes?: string;

    @ApiProperty({ description: "Check-In", example: false })
    @IsOptional()
    @Type(() => Boolean)
    @IsBoolean()
    checkIn?: boolean = false;

    @ApiProperty({ description: "MarkType", enum: MarkAvailabilityType, required: false })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsEnum(MarkAvailabilityType)
    markType?: MarkAvailabilityType;
}
