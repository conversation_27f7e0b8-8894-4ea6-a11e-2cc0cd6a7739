import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsArray, IsDate, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Max, Min, ValidateNested } from 'class-validator';
import { DiscountType } from 'src/utils/enums/discount.enum';
import { ENUM_PROMOTION_TARGET } from '../enums/promotion-target.enum';
import { ENUM_ITEM_TYPE } from '../enums/item-type.enum';

export class TimeWindowDto {
  @ApiProperty({
    description: 'Start time for promotion availability (HH:mm)',
    example: '08:00',
  })
  @IsString({ message: 'Start time must be a string' })
  @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'Start time must be in the format HH:mm' })
  startTime: string;

  @ApiProperty({
    description: 'End time for promotion availability (HH:mm)',
    example: '17:00',
  })
  @IsString({ message: 'End time must be a string' })
  @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'End time must be in the format HH:mm' })
  endTime: string;
}

export class CreatePromotionItemDto {
  @ApiProperty({
    description: 'Type of item',
    example: ENUM_ITEM_TYPE.SERVICE,
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'Item type is required' })
  @IsEnum(ENUM_ITEM_TYPE, { message: 'Invalid item type' })
  itemType: ENUM_ITEM_TYPE;

  @ApiProperty({
    description: 'ID of the item',
    example: '60d21b4667d0d8992e610c85',
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'Item ID is required' })
  itemId: string;
}

export class CreatePromotionDto {
  @ApiProperty({
    description: 'Name of the promotion',
    example: 'Student Discount',
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'Promotion name is required' })
  name: string;

  @ApiProperty({
    description: 'Description of the promotion',
    example: 'Special discount for students',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Type of promotion',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
    required: true,
  })
  // @IsEnum(DiscountType, { message: 'Invalid promotion type' })
  @Transform(({ value }) => {
    let x = "";
    if (['percentage', DiscountType.PERCENTAGE].includes(value)) x = DiscountType.PERCENTAGE;
    if (['flat', DiscountType.FLAT].includes(value)) x = DiscountType.FLAT;
    return x;
  })
  @IsNotEmpty({ message: 'Promotion discount type is required' })
  type: DiscountType;

  @ApiProperty({
    description: 'Value of the promotion (percentage or flat amount)',
    example: 15,
    required: true,
  })
  @IsNumber({}, { message: 'Promotion value must be a number' })
  @Min(0, { message: 'Promotion value cannot be negative' })
  value: number;

  @ApiProperty({
    description: 'Target audience for the promotion',
    enum: ENUM_PROMOTION_TARGET,
    example: ENUM_PROMOTION_TARGET.ALL_USERS,
    required: true,
  })
  // @IsEnum(ENUM_PROMOTION_TARGET, { message: 'Invalid promotion target' })
  @IsEnum(ENUM_PROMOTION_TARGET, { message: 'Invalid promotion target' })
  target: ENUM_PROMOTION_TARGET;

  @ApiProperty({
    description: 'Type of items for the promotion',
    enum: ENUM_ITEM_TYPE,
    example: ENUM_ITEM_TYPE.SERVICE,
    required: true,
  })
  @IsNotEmpty({ message: 'Item type is required' })
  @IsString()
  @IsEnum(ENUM_ITEM_TYPE, { message: 'Invalid item type' })
  itemType: ENUM_ITEM_TYPE;

  @ApiProperty({
    description: 'Start date of the promotion',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Transform(({ value }) => (value ? new Date(value) : new Date()), { toClassOnly: true })
  startDate?: Date;

  @ApiProperty({
    description: 'End date of the promotion',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  // @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
  endDate?: Date;

  // @ApiProperty({
  //   description: 'Time window for the promotion',
  //   required: false,
  // })
  // @IsOptional()
  // @ValidateNested()
  // @Type(() => TimeWindowDto)
  // timeWindow?: TimeWindowDto;

  @ApiProperty({
    description: 'Status of the promotion',
    example: true,
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  isActive?: boolean = true;

  @ApiProperty({
    description: 'Facility IDs where this promotion is applicable (empty means all facilities)',
    example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsNotEmpty({ each: true })
  facilityIds?: string[] = [];

  @ApiProperty({
    description: 'Items for the promotion',
    required: false,
    type: [CreatePromotionItemDto],
  })
  @IsOptional()
  @Type(() => CreatePromotionItemDto)
  items?: CreatePromotionItemDto[] = [];
  @ApiProperty({
    description: 'Name of the Label',
    example: 'Student Id',
    required: false,
  })
  @IsString()
  @IsOptional()
  promotionLabel: string;
}
