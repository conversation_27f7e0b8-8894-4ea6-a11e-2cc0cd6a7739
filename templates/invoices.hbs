<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f9f9f9;
        }

        .invoice-container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            overflow: hidden;
        }

        .header {
            text-align: left;
            word-wrap: break-word;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }

        .header p {
            margin: 5px 0;
            font-size: 14px;
            color: #555;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 20px;
            margin: 20px 0;
        }

        .info-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: #f9f9f9;
            word-wrap: break-word;
            overflow: hidden;
        }

        .info-box h3 {
            margin: 0 0 10px;
            font-size: 18px;
            color: #555;
        }

        .info-box p {
            margin: 5px 0;
            font-size: 14px;
            color: #555;
            white-space: normal;
            word-wrap: break-word;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .table th,
        .table td {
            padding: 10px;
            border: 1px solid #ddd;
            word-wrap: break-word;
            text-align: center;
            /* Align text in the center for both header and data cells */
        }

        .table th {
            /* background-color: #8143D1 !important; */
            font-size: 14px;
            font-weight: bold;
            color: rgb(255, 255, 255);
        }

        .table td {
            font-size: 14px;
            white-space: normal;
        }

        .table td:first-child,
        .table td:nth-child(2) {
            text-align: center;
            /* Optional: Ensure these two columns are also center aligned */
        }



        .totals {
            margin-top: 20px;
            text-align: right;
        }

        .totals p {
            margin: 2px 0;
            /* Reduce vertical spacing */
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            /* Align text and values properly */
            gap: 10px;
            /* Reduce horizontal gap */
        }

        .totals .grand-total {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .totals .Amount-In-Words {
            font-size: 18px;
            /* Set the font size to 18px */
            font-weight: bold;
            /* Ensure the text is bold */
            color: #333;
            text-align: left;
            /* Align text to the left */
            margin-left: 0;
            /* Remove any default margin to align fully to the left */
        }

        .totals .Amount-In-Words span {
            font-weight: bold;
            /* Make the amount in words bold */
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <div class="header">
            <h1 style="margin-bottom: 10px">Invoice</h1>
            <div>
                <div style="display: flex;">
                    <p style="width: 120px; color: rgb(163, 151, 151);">Invoice No:</p>
                    <p>{{orderId}}</p>
                </div>
                <div style="display: flex;">
                    <p style="width: 120px; color: rgb(163, 151, 151);">Order Date:</p>
                    <p>{{invoiceDate}}</p>
                </div>
                <div style="display: flex;">
                    <p style="width: 120px; color: rgb(163, 151, 151);">Created By:</p>
                    <p>{{createdByName}}</p>
                </div>
            </div>
        </div>
        <div class="info-section">
            <div class="info-box">
                <h3 style="color: #B095FC">Billed By</h3>

                <p><strong>{{billingDetails.billingName}}</strong></p>

                <p>
                    {{billingDetails.addressLine1}}
                    {{#if billingDetails.addressLine2}}, {{billingDetails.addressLine2}}{{/if}},
                    {{billingDetails.cityName}}, {{billingDetails.stateName}} - {{billingDetails.postalCode}}
                </p>

                <p>GSTIN: {{billingDetails.gstNumber}}</p>
                <p>State/UT Code: {{billingDetails.utCode}}</p>
                <p>Email: {{billingDetails.email}}</p>
                <p>Phone: {{billingDetails.phone}}</p>
                {{#unless clientBillingDetails.gstNumber}}
                <p>Place of Supply: {{billingDetails.stateName}}</p>
                {{/unless}}

            </div>
            <div class="info-box">
                <h3 style="color: #B095FC">Billed To</h3>

                {{#if clientBillingDetails.name}}
                <p><strong>{{clientBillingDetails.name}}</strong></p>
                {{/if}}

                {{#if clientBillingDetails.addressLine1}}
                <p>
                    {{clientBillingDetails.addressLine1}}
                    {{#if clientBillingDetails.addressLine2}},{{clientBillingDetails.addressLine2}}{{/if}}
                </p>
                {{/if}}

                {{#if clientBillingDetails.cityName}}
                <p>{{clientBillingDetails.cityName}}</p>
                {{/if}}

                {{#if clientBillingDetails.postalCode}}
                <p>{{clientBillingDetails.stateName}} - {{clientBillingDetails.postalCode}}</p>
                {{else}}
                <p>{{clientBillingDetails.stateName}}</p>
                {{/if}}
                {{#if clientBillingDetails.gstNumber}}<p>GSTIN: {{clientBillingDetails.gstNumber}}</p>{{/if}}
                <p>State/UT Code: {{clientBillingDetails.utCode}}</p>

                {{#if clientBillingDetails.email}}
                <p>Email: {{clientBillingDetails.email}}</p>
                {{/if}}

                <p>Phone: {{clientDetails.phone}}</p>
                {{#if clientBillingDetails.gstNumber}} <p>Place of Supply: {{clientBillingDetails.stateName}}</p>
                </p>{{/if}}
            </div>
        </div>

        <table class="table">
            <thead style="background-color: #8143D1;">
                <tr>
                    <th>Sr. No.</th>
                    <th>Item</th>
                    <th>QTY</th>
                    <th>Validity</th>
                    <th>Unit Price (₹)</th>
                    <th>Discount (₹)</th>
                    <th>HSN/SAC</th>
                    <th>GST%</th>
                </tr>
            </thead>
            <tbody>
                {{#each returnPurchaseItemsDetails}}
                <tr style="color: red;">
                    <td>{{inc @index}}</td>
                    <td>{{this.name}}</td>
                    <td>{{this.quantity}}</td>
                    <td>{{this.expireIn}} {{this.durationUnit}}</td>
                    <td>{{this.price}}</td>
                    <td>--</td>
                    <td>{{showZero this.hsnOrSacCode}}</td>
                    <td>{{showZero this.tax}}</td>

                </tr>
                {{/each}}
                {{#each purchaseItems}}
                <tr>
                    <td>{{inc @index}}</td>
                    <td>{{this.packageName}}</td>
                    <td>{{this.quantity}}</td>
                    <td>{{this.expireIn}} {{this.durationUnit}}</td>
                    <td>
                        {{#if this.isInclusiveofGst}}
                        {{this.finalPrice}}
                        {{else}}
                        {{this.unitPrice}}
                        {{/if}}
                    </td>
                    <td>{{this.discountExcludeCart}}</td>
                    <td>{{showZero this.hsnOrSacCode}}</td>
                    <td>{{showZero this.tax}}</td>
                </tr>
                {{/each}}
                {{#each customPackageItems}}
                <tr>
                    <td>{{inc @index}}</td>
                    <td>{{this.packageName}}</td>
                    <td>{{this.quantity}}</td>
                    <td>-</td> <!-- No validity for products -->
                    <td>{{this.unitPrice}}</td>
                    <td>{{this.discountExcludeCart}}</td>
                    <td>{{this.hsnOrSacCode}}</td>
                    <td>{{this.tax}}</td>
                </tr>
                {{/each}}
                {{#each productItem}}
                <tr>
                    <td>{{inc @index}}</td>
                    <td>{{this.productName}}</td>
                    <td>{{this.quantity}}</td>
                    <td>-</td> <!-- No validity for products -->
                    <td>{{this.salePrice}}</td>
                    <td>{{this.discountExcludeCart}}</td>
                    <td>{{showZero this.hsnOrSacCode}}</td>
                    <td>{{showZero this.tax}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="totals">
            <p><span>Sub Total:</span> <span>₹ {{subTotal}}</span></p>
            <p><span>Item Discount:</span> <span>₹ - {{itemDiscount}}</span></p>

            {{#if (eq cartDiscountType 'Flat')}}
            <p>
                <span>Cart Discount (₹{{cartDiscount}}):</span>
                <span>₹ - {{cartDiscountAmount}}</span>
            </p>
            {{else}}
            <p>
                <span>Cart Discount:</span>
                <span>₹ - {{cartDiscountAmount}}</span>
            </p>
            {{/if}}
            <p><span>Return Discount :</span> <span>₹ - {{returnDiscount}}</span></p>
            {{#if (neq clientBillingDetails.utCode billingDetails.utCode)}}
            <p><span>IGST:</span> <span>₹ {{igst}}</span></p>
            {{/if}}

            {{#if (eq clientBillingDetails.utCode billingDetails.utCode)}}
            <p><span>CGST:</span> <span>₹ {{cgst}}</span></p>
            {{/if}}

            {{#if (eq clientBillingDetails.utCode billingDetails.utCode)}}
            <p><span>SGST:</span> <span>₹ {{sgst}}</span></p>
            {{/if}}
            <p><span>Total</span> <span>₹ {{totalAmountAfterGst}}</span></p>
            <p><span>Round Off:</span> <span>₹ - {{roundOff}}</span></p>
            <p class="grand-total"><span>Grand Total:</span> <span>₹ {{grandTotal}}</span></p>
        </div>
        <p class="Amount-In-Words"><span style='font-weight: 700; color: #333;'>Amount In Words:</span>
            <span>{{amountInWords}}</span>
        </p>
    </div>
</body>

</html>